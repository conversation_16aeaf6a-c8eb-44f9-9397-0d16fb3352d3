class DropletInstalledJob < WebhookEventJob
  queue_as :default  # Override parent class to use inline adapter

  # payload - Hash received from the webhook controller.
  # Expected structure (example):
  # {
  #   "company" => {
  #     "fluid_shop" => "example.myshopify.com",
  #     "name" => "Example Shop",
  #     "fluid_company_id" => 123,
  #     "company_droplet_uuid" => "uuid",
  #     "authentication_token" => "token",
  #     "webhook_verification_token" => "verify",
  #   }
  # }
  def process_webhook
    Rails.logger.info("🚀 [DropletInstalledJob] Starting webhook processing")
    Rails.logger.info("🚀 [DropletInstalledJob] Payload: #{get_payload}")

    # Validate required keys in payload
    validate_payload_keys("company")
    company_attributes = get_payload.fetch("company", {})

    Rails.logger.info("🚀 [DropletInstalledJob] Company attributes: #{company_attributes}")

    # Create or update Company record in separate transaction
    company = nil
    integration_setting = nil

    ActiveRecord::Base.transaction do
      company = create_or_update_company(company_attributes)

      if company.persisted?
        Rails.logger.info("🚀 [DropletInstalledJob] Company created/updated successfully: ID=#{company.id}, fluid_company_id=#{company.fluid_company_id}")

        # Create or update AvalaraIntegrationSetting record
        integration_setting = create_or_update_avalara_setting(company)
        Rails.logger.info("🚀 [DropletInstalledJob] IntegrationSetting created/updated successfully")
      else
        Rails.logger.error("🚀 [DropletInstalledJob] Company failed to persist: #{company.errors.full_messages}")
        raise ActiveRecord::Rollback
      end
    end

    # Register callbacks OUTSIDE the transaction so failures don't rollback company creation
    if company&.persisted? && integration_setting
      Rails.logger.info("🚀 [DropletInstalledJob] Starting callback registration (outside transaction)")
      begin
        register_active_callbacks(company, integration_setting)
        Rails.logger.info("🚀 [DropletInstalledJob] Callback registration completed")
      rescue => e
        Rails.logger.error("🚀 [DropletInstalledJob] Callback registration failed, but company was created: #{e.message}")
        # Company and IntegrationSetting are already saved, so installation is partially successful
      end
    end
  end

private

  def create_or_update_company(company_attributes)
    Rails.logger.info("🏢 [DropletInstalledJob] Looking for company with fluid_shop: #{company_attributes['fluid_shop']}")
    Rails.logger.info("🏢 [DropletInstalledJob] Full company_attributes: #{company_attributes}")

    # Look for existing company by fluid_shop or droplet_installation_uuid
    company = Company.find_by(fluid_shop: company_attributes["fluid_shop"]) ||
              Company.find_by(droplet_installation_uuid: company_attributes["droplet_installation_uuid"]) ||
              Company.new

    if company.persisted?
      Rails.logger.info("🏢 [DropletInstalledJob] Found existing company: #{company.fluid_company_id}")
    else
      Rails.logger.info("🏢 [DropletInstalledJob] Creating new company")
    end

    company.assign_attributes(company_attributes.slice(
      "fluid_shop",
      "name",
      "fluid_company_id",
      "authentication_token",
      "webhook_verification_token",
      "droplet_installation_uuid"
    ))
    # Fix: Fluid sends "droplet_uuid" but we need "company_droplet_uuid"
    company.company_droplet_uuid = company_attributes.fetch("droplet_uuid")
    company.active = true

    Rails.logger.info("🏢 [DropletInstalledJob] Saving company with attributes: #{company.attributes.slice('fluid_shop',
'fluid_company_id', 'droplet_installation_uuid', 'company_droplet_uuid')}")

    if company.save
      Rails.logger.info("🏢 [DropletInstalledJob] Company saved successfully: ID=#{company.id}")
    else
      Rails.logger.error("🏢 [DropletInstalledJob] Failed to create company: #{company.errors.full_messages.join(', ')}")
    end

    company
  end

  def create_or_update_avalara_setting(company)
    integration_setting = IntegrationSetting.find_or_initialize_by(
      company_id: company.fluid_company_id
    )

    # Set default values for new installations
    if integration_setting.new_record?
      integration_setting.assign_attributes(
        enabled: false,                    # Disabled by default until configured
        configuration: default_avalara_configuration
      )

      Rails.logger.info(
        "[DropletInstalledJob] Creating new IntegrationSetting for company_id: #{company.fluid_company_id}"
      )
    else
      Rails.logger.info(
        "[DropletInstalledJob] Updating existing IntegrationSetting for company_id: #{company.fluid_company_id}"
      )
    end

    integration_setting
  end

private

  def register_active_callbacks(company, integration_setting)
    # Validate company exists and has authentication token
    unless company&.persisted? && company.authentication_token.present?
      Rails.logger.error(
        "[DropletInstalledJob] Cannot register callbacks: company is nil or missing authentication_token"
      )
      return []
    end

    active_callbacks = ::Callback.active

    if active_callbacks.empty?
      Rails.logger.warn(
        "[DropletInstalledJob] No active callbacks found. " \
        "Run 'CallbackSyncService.new.sync' and activate callbacks in admin panel."
      )
      return []
    end

    Rails.logger.info(
      "[DropletInstalledJob] Found #{active_callbacks.count} active callbacks for company #{company.fluid_company_id}: " \
      "#{active_callbacks.pluck(:name).join(', ')}"
    )

    client = FluidClient.new(company.authentication_token)
    installed_callback_ids = []

    active_callbacks.each do |callback|
      begin
        callback_attributes = {
          definition_name: callback.name,
          url: callback.url,
          timeout_in_seconds: callback.timeout_in_seconds,
          active: true,
        }

        response = client.callback_registrations.create(callback_attributes)
        if response && response["callback_registration"]["uuid"]
          installed_callback_ids << response["callback_registration"]["uuid"]
        else
          Rails.logger.warn(
            "[DropletInstalledJob] Callback registered but no UUID returned for: #{callback.name}"
          )
        end
      rescue => e
        Rails.logger.error(
          "[DropletInstalledJob] Failed to register callback #{callback.name}: #{e.message}"
        )
      end
    end

    if installed_callback_ids.any?
      company.update(installed_callback_ids: installed_callback_ids)
    end

    # Always update these fields on installation/reinstallation
    integration_setting.droplet_installation_uuid = company.droplet_installation_uuid
    integration_setting.authentication_token = company.authentication_token

    unless integration_setting.save
      Rails.logger.error(
        "[DropletInstalledJob] Failed to create/update IntegrationSetting: " \
        "#{integration_setting.errors.full_messages.join(', ')}"
      )
    end

    integration_setting
  end



  def default_avalara_configuration
    {
      "enabled" => false,
      "fallback_to_manual" => true,
      "include_shipping_tax" => true,
      "shipping_tax_code" => "FR030000",
      "debug_logging" => false,
      "timeout_seconds" => 30,
      "auto_commit_transactions" => false,
      "created_via" => "droplet_installation",
      "installation_date" => Time.current.iso8601,
    }
  end
end
