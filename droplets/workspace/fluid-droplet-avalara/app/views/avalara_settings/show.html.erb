<% content_for :title, "Avalara Configuration" %>

<!-- Hidden data attributes for JavaScript to access authentication headers -->
<div id="auth-data"
     data-authorization="<%= request.headers['Authorization'] %>"
     data-company-id="<%= request.headers['X-Fluid-Company-Id'] %>"
     data-user-id="<%= request.headers['X-Fluid-User-Id'] %>"
     data-dri="<%= params[:dri] %>"
     style="display: none;"></div>

<div class="max-w-7xl mx-auto p-6">
  <div class="bg-white shadow-lg rounded-lg">
    <!-- Header -->
    <div class="border-b border-gray-200 px-6 py-4">
      <h1 class="text-2xl font-bold text-gray-900">Avalara Tax Configuration</h1>
      <p class="text-gray-600 mt-1">Configure your Avalara credentials and monitor tax calculations</p>

      <!-- Company Information -->
      <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
          <span class="text-sm font-medium text-blue-800">Company ID:</span>
          <span class="text-sm text-blue-700 ml-2 font-mono"><%= @company_id %></span>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="border-b border-gray-200">
      <nav class="flex space-x-8 px-6" aria-label="Tabs">
        <button id="config-tab" class="tab-button active border-b-2 border-blue-500 py-4 px-1 text-sm font-medium text-blue-600">
          <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          Configuration
        </button>
        <button id="logs-tab" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
          <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Tax Calculation Logs
        </button>
        <button id="activity-tab" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
          <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          Activity Logs
        </button>
        <button id="blacklist-tab" class="tab-button border-b-2 border-transparent py-4 px-1 text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300">
          <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18 12M6 6l12 12"></path>
          </svg>
          Product Blacklist
        </button>
      </nav>
    </div>



    <!-- Flash Messages (visible across all tabs) -->
    <% if flash[:success] %>
      <div id="flash-success" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mx-6 mt-4">
        <div class="flex justify-between items-center">
          <span><%= flash[:success] %></span>
          <button onclick="document.getElementById('flash-success').style.display='none'" class="text-green-700 hover:text-green-900">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    <% end %>

    <% if flash[:error] %>
      <div id="flash-error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mx-6 mt-4">
        <div class="flex justify-between items-center">
          <span><%= flash[:error] %></span>
          <button onclick="document.getElementById('flash-error').style.display='none'" class="text-red-700 hover:text-red-900">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
    <% end %>

    <!-- Tab Content -->
    <div id="config-content" class="tab-content">

      <!-- Configuration Form -->
      <div class="p-6">
      <%= form_with model: @integration_setting,
                    url: @integration_setting.persisted? ? avalara_configure_path : avalara_configure_path,
                    method: @integration_setting.persisted? ? :patch : :post,
                    local: true,
                    scope: :integration_setting,
                    class: "space-y-6" do |form| %>

        <!-- Hidden DRI field to maintain authentication -->
        <% dri_value = session[:droplet_installation_uuid] || params[:dri] || (Company.count == 1 ? Company.first.droplet_installation_uuid : nil) %>
        <%= hidden_field_tag :dri, dri_value %>

        <!-- Status Section -->
        <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Integration Status</h3>
            <p class="text-sm text-gray-600">
              Environment: <span class="font-mono text-blue-600"><%= Rails.env.production? ? 'Production' : 'Sandbox' %></span>
            </p>
          </div>
          <div class="flex items-center">
            <%= form.check_box :enabled,
                              class: "h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" %>
            <%= form.label :enabled, "Enable Avalara Integration", class: "ml-3 text-sm font-medium text-gray-700" %>
          </div>
        </div>

        <!-- Credentials Section -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Avalara Credentials</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Account ID</label>
              <div class="relative">
                <%= form.password_field :account_id,
                                       value: @integration_setting.account_id,
                                       class: "w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500",
                                       placeholder: "Your Avalara Account ID" %>
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="integration_setting_account_id">
                  <svg class="h-5 w-5 text-gray-400 eye-open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <svg class="h-5 w-5 text-gray-400 eye-closed hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656l1.415 1.415m-1.415-1.415l1.415 1.415M14.828 14.828L16.243 16.243" />
                  </svg>
                </button>
              </div>
              <% if @integration_setting.errors[:account_id].any? %>
                <p class="text-red-500 text-xs mt-1"><%= @integration_setting.errors[:account_id].first %></p>
              <% end %>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">License Key</label>
              <div class="relative">
                <%= form.password_field :license_key,
                                       value: @integration_setting.license_key,
                                       class: "w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500",
                                       placeholder: "Your Avalara License Key" %>
                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="integration_setting_license_key">
                  <svg class="h-5 w-5 text-gray-400 eye-open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  <svg class="h-5 w-5 text-gray-400 eye-closed hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656l1.415 1.415m-1.415-1.415l1.415 1.415M14.828 14.828L16.243 16.243" />
                  </svg>
                </button>
              </div>
              <% if @integration_setting.errors[:license_key].any? %>
                <p class="text-red-500 text-xs mt-1"><%= @integration_setting.errors[:license_key].first %></p>
              <% end %>
            </div>
          </div>

          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Company Code</label>
            <div class="relative w-full md:w-1/2">
              <%= form.password_field :company_code,
                                     value: @integration_setting.company_code,
                                     class: "w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500",
                                     placeholder: "Your Avalara Company Code" %>
              <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center toggle-password" data-target="integration_setting_company_code">
                <svg class="h-5 w-5 text-gray-400 eye-open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <svg class="h-5 w-5 text-gray-400 eye-closed hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414L8.464 8.464m5.656 5.656l1.415 1.415m-1.415-1.415l1.415 1.415M14.828 14.828L16.243 16.243" />
                </svg>
              </button>
            </div>
            <% if @integration_setting.errors[:company_code].any? %>
              <p class="text-red-500 text-xs mt-1"><%= @integration_setting.errors[:company_code].first %></p>
            <% end %>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="border-t border-gray-200 pt-8 mt-8">
          <!-- Test Buttons Row -->
          <div class="flex justify-between items-center mb-4">
            <div class="flex space-x-3">
              <button type="button"
                      id="test-connection-btn"
                      class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Test Connection
              </button>

              <button type="button"
                      id="test-callback-btn"
                      class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                Test Callback
              </button>

              <button type="button"
                      id="sync-status-btn"
                      class="btn btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Sync Status
              </button>
            </div>
          </div>

          <!-- Hidden field for product blacklist -->
          <input type="hidden" id="product_blacklist_field" name="integration_setting[product_blacklist]" value="">

          <!-- Save/Cancel Row -->
          <div class="flex justify-end items-center space-x-3">
            <%= link_to "Cancel", root_path,
                       class: "btn btn-cancel" %>
            <%= form.submit @integration_setting.persisted? ? "Update Configuration" : "Save Configuration",
                           class: "btn btn-primary" %>
          </div>
        </div>
      <% end %>
      </div>
    </div>

    <!-- Logs Tab Content -->
    <div id="logs-content" class="tab-content hidden">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">Tax Calculation Logs</h3>
          <div class="flex space-x-3">
            <button id="export-logs-btn" class="btn btn-secondary">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Export
            </button>

            <button id="clear-logs-btn" class="btn btn-danger">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
              Clear
            </button>

            <button id="refresh-logs" class="btn btn-secondary">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh
            </button>
          </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-center">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div class="ml-3">
                <p class="text-sm font-medium text-green-800">Successful</p>
                <p class="text-2xl font-bold text-green-900" id="success-count">-</p>
              </div>
            </div>
          </div>

          <div class="bg-red-50 border border-red-200 rounded-lg p-4">
            <div class="flex items-center">
              <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              <div class="ml-3">
                <p class="text-sm font-medium text-red-800">Failed</p>
                <p class="text-2xl font-bold text-red-900" id="error-count">-</p>
              </div>
            </div>
          </div>

          <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-center">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
              <div class="ml-3">
                <p class="text-sm font-medium text-blue-800">Avg Response</p>
                <p class="text-2xl font-bold text-blue-900" id="avg-response">-</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Logs Table -->
        <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
            <h4 class="text-sm font-medium text-gray-900">Recent Tax Calculations</h4>
          </div>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" style="min-width: 1200px;">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cart ID</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Total</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response Time</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Items</th>
                  <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                </tr>
              </thead>
              <tbody id="logs-table-body" class="bg-white divide-y divide-gray-200">
                <tr>
                  <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                    <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>
                    Click "Refresh" to load recent tax calculation logs
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Activity Logs Tab Content -->
  <div id="activity-content" class="tab-content hidden">
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-lg font-medium text-gray-900">Activity Logs</h3>
        <div class="flex space-x-3">
          <button id="export-activity-logs-btn" class="btn btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export
          </button>

          <button id="clear-activity-logs-btn" class="btn btn-danger">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Clear
          </button>

          <button id="refresh-activity-logs" class="btn btn-secondary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh
          </button>
        </div>
      </div>

      <!-- Activity Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Successful</p>
              <p class="text-2xl font-semibold text-gray-900" id="activity-success-count">-</p>
            </div>
          </div>
        </div>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Failed</p>
              <p class="text-2xl font-semibold text-gray-900" id="activity-error-count">-</p>
            </div>
          </div>
        </div>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Warnings</p>
              <p class="text-2xl font-semibold text-gray-900" id="activity-warning-count">-</p>
            </div>
          </div>
        </div>
        <div class="bg-white border border-gray-200 rounded-lg p-4">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Total</p>
              <p class="text-2xl font-semibold text-gray-900" id="activity-total-count">-</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Activity Logs Table -->
      <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
          <h4 class="text-sm font-medium text-gray-900">Recent Activity</h4>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200" style="min-width: 1000px;">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Message</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
              </tr>
            </thead>
            <tbody id="activity-logs-table-body" class="bg-white divide-y divide-gray-200">
              <tr>
                <td colspan="5" class="px-6 py-4 text-center text-gray-500">
                  Loading activity logs...
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Product Blacklist Tab Content -->
  <div id="blacklist-content" class="tab-content hidden">
    <div class="p-6">
      <div class="flex justify-between items-center mb-6">
        <div>
          <h3 class="text-lg font-medium text-gray-900">Product Blacklist</h3>
          <p class="text-sm text-gray-600 mt-1">Products in this list will be excluded from tax calculations</p>
        </div>
        <div class="flex space-x-3">
          <button id="clear-blacklist-btn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            Clear All
          </button>
          <button id="refresh-blacklist-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            Refresh
          </button>
        </div>
      </div>

      <!-- Add Products Section -->
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <div class="flex justify-between items-center mb-4">
          <h4 class="text-md font-medium text-gray-900">Add Products to Blacklist</h4>
          <div class="flex space-x-2">
            <button id="toggle-manual-mode" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
              Switch to Manual Entry
            </button>
          </div>
        </div>

        <!-- Visual Product Selection (Default) -->
        <div id="visual-selection-mode" class="space-y-4">
          <!-- Search Bar -->
          <div class="flex space-x-3">
            <div class="flex-1">
              <input type="text"
                     id="product-search-input"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                     placeholder="Search products by name, SKU, or ID...">
            </div>
            <button id="search-products-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
              Search
            </button>
          </div>

          <!-- Products Count -->
          <div id="products-count" class="text-sm text-gray-600 mb-2 hidden">
            Showing <span id="products-count-number">0</span> products
          </div>

          <!-- Products List -->
          <div id="products-container" class="border border-gray-200 rounded-md max-h-96 overflow-y-auto">
            <div id="products-loading" class="p-4 text-center text-gray-500 hidden">
              <svg class="w-6 h-6 mx-auto mb-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading products...
            </div>
            <div id="products-list" class="divide-y divide-gray-200">
              <!-- Products will be loaded here -->
            </div>
            <div id="products-empty" class="p-4 text-center text-gray-500 hidden">
              No products found. Try a different search term.
            </div>
          </div>

          <!-- Pagination -->
          <div id="products-pagination" class="hidden mt-4 flex justify-between items-center">
            <button id="prev-page" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Previous
            </button>
            <span id="page-info" class="text-sm text-gray-600">
              Page 1 of 1
            </span>
            <button id="next-page" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Next
            </button>
          </div>

          <!-- Selected Products Actions -->
          <div class="flex justify-between items-center">
            <span id="selected-count" class="text-sm text-gray-600">0 products selected</span>
            <button id="add-selected-btn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed" disabled>
              Add Selected to Blacklist
            </button>
          </div>
        </div>

        <!-- Manual Entry Mode (Hidden by default) -->
        <div id="manual-entry-mode" class="hidden space-y-4">
          <div class="flex space-x-3">
            <div class="flex-1">
              <textarea id="product-ids-input"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        rows="3"
                        placeholder="Enter product IDs (one per line or comma-separated)&#10;Example:&#10;PROD-001&#10;PROD-002, PROD-003"></textarea>
            </div>
            <button id="add-to-blacklist-btn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md text-sm font-medium self-start">
              Add to Blacklist
            </button>
          </div>
          <p class="text-xs text-gray-500">
            You can enter multiple product IDs separated by commas or new lines
          </p>
        </div>
      </div>

      <!-- Stats -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-white p-4 rounded-lg border border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18 12M6 6l12 12"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Blacklisted Products</p>
              <p class="text-2xl font-semibold text-gray-900" id="blacklist-count">0</p>
            </div>
          </div>
        </div>
        <div class="bg-white p-4 rounded-lg border border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Status</p>
              <p class="text-sm font-semibold text-gray-900" id="blacklist-status">Active</p>
            </div>
          </div>
        </div>
        <div class="bg-white p-4 rounded-lg border border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500">Last Updated</p>
              <p class="text-sm font-semibold text-gray-900" id="blacklist-updated">Never</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Blacklist Table -->
      <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <div class="px-4 py-5 sm:p-6">
          <h4 class="text-md font-medium text-gray-900 mb-4">Current Blacklist</h4>
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product ID</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Added</th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody id="blacklist-table-body" class="bg-white divide-y divide-gray-200">
                <tr>
                  <td colspan="3" class="px-6 py-4 text-center text-gray-500">
                    <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m-2 0v5a2 2 0 002 2h2M4 13V8a2 2 0 012-2h2m0 0V4a2 2 0 012-2h4a2 2 0 012 2v2m0 0h2a2 2 0 012 2v5M8 7h8m-8 4h8m-4 8v-4"></path>
                    </svg>
                    Click "Refresh" to load current blacklist
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Connection Test Result -->
  <div id="test-result" class="mt-4 hidden">
    <div id="test-success" class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded hidden">
      <strong>Success!</strong> <span id="success-message"></span>
    </div>
    <div id="test-error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded hidden">
      <strong>Error:</strong> <span id="error-message"></span>
    </div>
  </div>
</div>

<style>
.tab-button.active {
  color: #2563eb;
  border-color: #2563eb;
}

.tab-content {
  display: block;
}

.tab-content.hidden {
  display: none;
}

.expand-arrow {
  transition: transform 0.2s ease;
}

.expand-arrow.rotate-90 {
  transform: rotate(90deg);
}

.log-row:hover {
  background-color: #f9fafb;
}

/* Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  text-decoration: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover:not(:disabled) {
  background-color: #1d4ed8;
  border-color: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
  border-color: #6b7280;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #4b5563;
  border-color: #4b5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
}

.btn-cancel {
  background-color: #f3f4f6;
  color: #374151;
  border-color: #d1d5db;
}

.btn-cancel:hover:not(:disabled) {
  background-color: #e5e7eb;
  border-color: #9ca3af;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(156, 163, 175, 0.3);
}

.btn-danger {
  background-color: #dc2626;
  color: white;
  border-color: #dc2626;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
  border-color: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.btn svg {
  flex-shrink: 0;
}
</style>

<script>
// Global functions that need to be accessible from onclick handlers

// Helper function to build URL with dri parameter
function buildUrlWithDri(baseUrl) {
  const authData = document.getElementById('auth-data');
  const dri = authData.dataset.dri;
  if (dri) {
    const separator = baseUrl.includes('?') ? '&' : '?';
    return `${baseUrl}${separator}dri=${encodeURIComponent(dri)}`;
  }
  return baseUrl;
}

// Helper function to get auth headers
function getAuthHeaders() {
  const authData = document.getElementById('auth-data');
  const headers = {
    'X-Requested-With': 'XMLHttpRequest'
  };

  if (authData.dataset.dri) {
    headers['X-DRI'] = authData.dataset.dri;
  }

  return headers;
}

// Show notification function - MOVED TO GLOBAL SCOPE
function showNotification(message, type) {
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 px-4 py-3 rounded-md shadow-lg z-50 ${
    type === 'success' ? 'bg-green-50 border border-green-200 text-green-700' :
    type === 'error' ? 'bg-red-50 border border-red-200 text-red-700' :
    'bg-blue-50 border border-blue-200 text-blue-700'
  }`;
  notification.innerHTML = `
    <div class="flex items-center">
      <span>${message}</span>
      <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-current hover:opacity-75">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  `;
  document.body.appendChild(notification);

  // Auto-remove after 5 seconds
  setTimeout(() => {
    if (notification.parentElement) {
      notification.remove();
    }
  }, 5000);
}

// Product Blacklist Functions - MOVED TO GLOBAL SCOPE
function loadProductBlacklist() {
  const refreshBtn = document.getElementById('refresh-blacklist-btn');
  const tableBody = document.getElementById('blacklist-table-body');
  const countElement = document.getElementById('blacklist-count');

  if (!refreshBtn || !tableBody || !countElement) {
    console.warn('Blacklist elements not found, skipping load');
    return;
  }

  refreshBtn.disabled = true;
  refreshBtn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...';

  fetch(buildUrlWithDri('/avalara/product_blacklist'), {
    method: 'GET',
    headers: getAuthHeaders()
  })
  .then(response => response.json())
  .then(data => {
    // Update count
    countElement.textContent = data.count || 0;
    document.getElementById('blacklist-updated').textContent = new Date().toLocaleString();

    // Update table
    if (data.blacklist.length === 0) {
      tableBody.innerHTML = `
        <tr>
          <td colspan="3" class="px-6 py-8 text-center text-gray-500">
            <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m-2 0v5a2 2 0 002 2h2M4 13V8a2 2 0 012-2h2m0 0V4a2 2 0 012-2h4a2 2 0 012 2v2m0 0h2a2 2 0 012 2v5M8 7h8m-8 4h8m-4 8v-4"></path>
            </svg>
            No products in blacklist
          </td>
        </tr>
      `;
    } else {
      tableBody.innerHTML = data.blacklist.map(productId => `
        <tr>
          <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${productId}</td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Just now</td>
          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
            <button onclick="removeFromBlacklist('${productId}')" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 hover:text-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 cursor-pointer transition-colors duration-200">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
              Remove
            </button>
          </td>
        </tr>
      `).join('');
    }
  })
  .catch(error => {
    showNotification('Error loading blacklist: ' + error.message, 'error');
    tableBody.innerHTML = `
      <tr>
        <td colspan="3" class="px-6 py-4 text-center text-red-500">
          Error loading blacklist
        </td>
      </tr>
    `;
  })
  .finally(() => {
    refreshBtn.disabled = false;
    refreshBtn.innerHTML = 'Refresh';
  });
}

function removeFromBlacklist(productId) {
  if (!confirm(`Are you sure you want to remove "${productId}" from the blacklist?`)) {
    return;
  }

  fetch(buildUrlWithDri('/avalara/product_blacklist/remove'), {
    method: 'DELETE',
    headers: {
      ...getAuthHeaders(),
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ product_ids: [productId] })
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      showNotification(data.message, 'success');
      loadProductBlacklist(); // Refresh the list
    } else {
      showNotification('Error: ' + data.message, 'error');
    }
  })
  .catch(error => {
    showNotification('Error removing from blacklist: ' + error.message, 'error');
  });
}

document.addEventListener('DOMContentLoaded', function() {
  // Tab functionality
  const configTab = document.getElementById('config-tab');
  const logsTab = document.getElementById('logs-tab');
  const activityTab = document.getElementById('activity-tab');
  const blacklistTab = document.getElementById('blacklist-tab');
  const configContent = document.getElementById('config-content');
  const logsContent = document.getElementById('logs-content');
  const activityContent = document.getElementById('activity-content');
  const blacklistContent = document.getElementById('blacklist-content');

  function switchTab(activeTab, activeContent) {
    // Reset all tabs
    [configTab, logsTab, activityTab, blacklistTab].forEach(tab => {
      tab.classList.remove('active', 'text-blue-600', 'border-blue-500');
      tab.classList.add('text-gray-500', 'border-transparent');
    });

    // Reset all content
    [configContent, logsContent, activityContent, blacklistContent].forEach(content => {
      content.classList.add('hidden');
    });

    // Activate selected tab
    activeTab.classList.add('active', 'text-blue-600', 'border-blue-500');
    activeTab.classList.remove('text-gray-500', 'border-transparent');
    activeContent.classList.remove('hidden');
  }

  configTab.addEventListener('click', function() {
    switchTab(configTab, configContent);
  });

  logsTab.addEventListener('click', function() {
    switchTab(logsTab, logsContent);
    loadLogs();
  });

  activityTab.addEventListener('click', function() {
    switchTab(activityTab, activityContent);
    loadActivityLogs();
  });

  blacklistTab.addEventListener('click', function() {
    switchTab(blacklistTab, blacklistContent);
    loadProductBlacklist();
  });

  // Refresh logs functionality
  document.getElementById('refresh-logs').addEventListener('click', function() {
    loadLogs();
  });

  // Refresh activity logs functionality
  document.getElementById('refresh-activity-logs').addEventListener('click', function() {
    loadActivityLogs();
  });

  // Test Callback functionality
  document.getElementById('test-callback-btn').addEventListener('click', function() {
    testCallback();
  });

  // Sync Status functionality
  document.getElementById('sync-status-btn').addEventListener('click', function() {
    syncStatus();
  });

  // Export Logs functionality
  document.getElementById('export-logs-btn').addEventListener('click', function() {
    exportLogs('tax_calculation');
  });

  // Clear Logs functionality
  document.getElementById('clear-logs-btn').addEventListener('click', function() {
    clearLogs('tax_calculation');
  });

  // Export Activity Logs functionality
  document.getElementById('export-activity-logs-btn').addEventListener('click', function() {
    exportLogs('activity');
  });

  // Clear Activity Logs functionality
  document.getElementById('clear-activity-logs-btn').addEventListener('click', function() {
    clearLogs('activity');
  });



  function loadLogs() {
    const refreshBtn = document.getElementById('refresh-logs');
    const tableBody = document.getElementById('logs-table-body');

    refreshBtn.disabled = true;
    refreshBtn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...';

    // Load real logs from API
    fetch(buildUrlWithDri('/avalara/logs'), {
      method: 'GET',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      // Update stats
      document.getElementById('success-count').textContent = data.stats.successful || 0;
      document.getElementById('error-count').textContent = data.stats.failed || 0;
      document.getElementById('avg-response').textContent = (data.stats.avg_response_time || 0) + 'ms';

      // Update table with expand/collapse functionality
      if (data.logs.length === 0) {
        tableBody.innerHTML = `
          <tr>
            <td colspan="7" class="px-6 py-4 text-center text-gray-500">
              <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              No tax calculation logs found
            </td>
          </tr>
        `;
      } else {
        tableBody.innerHTML = data.logs.map(log => `
          <tr class="log-row cursor-pointer hover:bg-gray-50" onclick="toggleLogDetails(${log.id})">
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-2 transform transition-transform expand-arrow" id="arrow-${log.id}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                ${formatTimestamp(log.timestamp)}
              </div>
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
              ${log.cart_id}
            </td>
            <td class="px-4 py-4 whitespace-nowrap">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${log.status === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                ${log.status}
              </span>
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
              ${log.tax_total}
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
              ${log.response_time}
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
              ${log.items_count}
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
              ${log.location}
            </td>
          </tr>
          <tr class="log-details hidden" id="details-${log.id}">
            <td colspan="7" class="px-6 py-4 bg-gray-50">
              <div class="space-y-4">
                ${log.error_message ? `
                  <div class="bg-red-50 border border-red-200 rounded p-3">
                    <h4 class="font-medium text-red-800">Error Message:</h4>
                    <p class="text-red-700 text-sm mt-1">${log.error_message}</p>
                  </div>
                ` : ''}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 class="font-medium text-gray-800 mb-2">Request Payload:</h4>
                    <pre class="bg-white border rounded p-2 text-xs overflow-auto max-h-40">${JSON.stringify(log.request_payload, null, 2)}</pre>
                  </div>
                  <div>
                    <h4 class="font-medium text-gray-800 mb-2">Response Payload:</h4>
                    <pre class="bg-white border rounded p-2 text-xs overflow-auto max-h-40">${JSON.stringify(log.response_payload, null, 2)}</pre>
                  </div>
                  ${log.avalara_request ? `
                    <div>
                      <h4 class="font-medium text-gray-800 mb-2">Avalara Request:</h4>
                      <pre class="bg-white border rounded p-2 text-xs overflow-auto max-h-40">${JSON.stringify(log.avalara_request, null, 2)}</pre>
                    </div>
                  ` : ''}
                  ${log.avalara_response ? `
                    <div>
                      <h4 class="font-medium text-gray-800 mb-2">Avalara Response:</h4>
                      <pre class="bg-white border rounded p-2 text-xs overflow-auto max-h-40">${JSON.stringify(log.avalara_response, null, 2)}</pre>
                    </div>
                  ` : ''}
                </div>
              </div>
            </td>
          </tr>
        `).join('');
      }

      refreshBtn.disabled = false;
      refreshBtn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Refresh';
    })
    .catch(error => {
      console.error('Error loading logs:', error);
      tableBody.innerHTML = `
        <tr>
          <td colspan="7" class="px-6 py-4 text-center text-red-500">
            Error loading logs. Please try again.
          </td>
        </tr>
      `;
      refreshBtn.disabled = false;
      refreshBtn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Refresh';
    });
  }

  // Make toggleLogDetails global
  window.toggleLogDetails = function(logId) {
    const detailsRow = document.getElementById(`details-${logId}`);
    const arrow = document.getElementById(`arrow-${logId}`);

    if (detailsRow.classList.contains('hidden')) {
      detailsRow.classList.remove('hidden');
      arrow.classList.add('rotate-90');
    } else {
      detailsRow.classList.add('hidden');
      arrow.classList.remove('rotate-90');
    }
  }
  const testBtn = document.getElementById('test-connection-btn');
  const testResult = document.getElementById('test-result');
  const testSuccess = document.getElementById('test-success');
  const testError = document.getElementById('test-error');

  testBtn.addEventListener('click', function() {
    testBtn.disabled = true;
    testBtn.textContent = 'Testing...';
    
    fetch(buildUrlWithDri('/avalara/test_connection'), {
      method: 'POST',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      testResult.classList.remove('hidden');
      
      if (data.success) {
        testSuccess.classList.remove('hidden');
        testError.classList.add('hidden');
        document.getElementById('success-message').textContent = data.message;
      } else {
        testError.classList.remove('hidden');
        testSuccess.classList.add('hidden');
        document.getElementById('error-message').textContent = data.message;
      }
    })
    .catch(error => {
      testResult.classList.remove('hidden');
      testError.classList.remove('hidden');
      testSuccess.classList.add('hidden');
      document.getElementById('error-message').textContent = 'Connection test failed';
    })
    .finally(() => {
      testBtn.disabled = false;
      testBtn.textContent = 'Test Connection';
    });
  });

  // Password toggle functionality
  document.querySelectorAll('.toggle-password').forEach(button => {
    button.addEventListener('click', function() {
      const targetId = this.getAttribute('data-target');
      const input = document.getElementById(targetId);
      const eyeOpen = this.querySelector('.eye-open');
      const eyeClosed = this.querySelector('.eye-closed');

      if (input.type === 'password') {
        input.type = 'text';
        eyeOpen.classList.add('hidden');
        eyeClosed.classList.remove('hidden');
      } else {
        input.type = 'password';
        eyeOpen.classList.remove('hidden');
        eyeClosed.classList.add('hidden');
      }
    });
  });

  // Show flash messages if they exist (in case they were hidden by tab switching)
  function showFlashMessages() {
    const flashSuccess = document.getElementById('flash-success');
    const flashError = document.getElementById('flash-error');

    if (flashSuccess) {
      flashSuccess.style.display = 'block';
      flashSuccess.style.opacity = '1';
    }

    if (flashError) {
      flashError.style.display = 'block';
      flashError.style.opacity = '1';
    }
  }

  // Show flash messages on page load
  showFlashMessages();

  // Auto-hide flash messages after 5 seconds
  setTimeout(function() {
    const flashSuccess = document.getElementById('flash-success');
    const flashError = document.getElementById('flash-error');

    if (flashSuccess) {
      flashSuccess.style.opacity = '0';
      flashSuccess.style.transition = 'opacity 0.5s ease-out';
      setTimeout(() => flashSuccess.style.display = 'none', 500);
    }

    if (flashError) {
      flashError.style.opacity = '0';
      flashError.style.transition = 'opacity 0.5s ease-out';
      setTimeout(() => flashError.style.display = 'none', 500);
    }
  }, 5000);

  // Load activity logs function
  function loadActivityLogs() {
    const refreshBtn = document.getElementById('refresh-activity-logs');
    const tableBody = document.getElementById('activity-logs-table-body');

    refreshBtn.disabled = true;
    refreshBtn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Loading...';

    // Load activity logs from API
    fetch(buildUrlWithDri('/avalara/activity_logs'), {
      method: 'GET',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      // Update stats
      document.getElementById('activity-success-count').textContent = data.stats.successful || 0;
      document.getElementById('activity-error-count').textContent = data.stats.failed || 0;
      document.getElementById('activity-warning-count').textContent = data.stats.warnings || 0;
      document.getElementById('activity-total-count').textContent = data.stats.total || 0;

      // Update table
      if (data.logs.length === 0) {
        tableBody.innerHTML = `
          <tr>
            <td colspan="5" class="px-6 py-4 text-center text-gray-500">
              <svg class="w-6 h-6 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              No activity logs found
            </td>
          </tr>
        `;
      } else {
        tableBody.innerHTML = data.logs.map(log => `
          <tr class="hover:bg-gray-50">
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
              ${formatTimestamp(log.timestamp)}
            </td>
            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
              <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
                ${log.activity_type}
              </span>
            </td>
            <td class="px-4 py-4 whitespace-nowrap">
              <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusClass(log.status)}">
                ${log.status}
              </span>
            </td>
            <td class="px-4 py-4 text-sm text-gray-900">
              ${log.message}
            </td>
            <td class="px-4 py-4 text-sm text-gray-500">
              ${log.details || 'N/A'}
            </td>
          </tr>
        `).join('');
      }

      refreshBtn.disabled = false;
      refreshBtn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Refresh';
    })
    .catch(error => {
      console.error('Error loading activity logs:', error);
      tableBody.innerHTML = `
        <tr>
          <td colspan="5" class="px-6 py-4 text-center text-red-500">
            Error loading activity logs. Please try again.
          </td>
        </tr>
      `;
      refreshBtn.disabled = false;
      refreshBtn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Refresh';
    });
  }

  function getStatusClass(status) {
    switch(status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'info': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  }

  // Test Callback function
  function testCallback() {
    const btn = document.getElementById('test-callback-btn');
    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Testing...';

    fetch(buildUrlWithDri('/avalara/test_callback'), {
      method: 'POST',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification('Callback test successful!', 'success');
      } else {
        showNotification('Callback test failed: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showNotification('Error testing callback: ' + error.message, 'error');
    })
    .finally(() => {
      btn.disabled = false;
      btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path></svg>Test Callback';
    });
  }

  // Sync Status function
  function syncStatus() {
    const btn = document.getElementById('sync-status-btn');
    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Syncing...';

    fetch(buildUrlWithDri('/avalara/sync_status'), {
      method: 'POST',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification('Sync status: ' + data.message, 'success');
      } else {
        showNotification('Sync failed: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showNotification('Error checking sync status: ' + error.message, 'error');
    })
    .finally(() => {
      btn.disabled = false;
      btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Sync Status';
    });
  }

  // Export Logs function
  function exportLogs(type) {
    const btn = document.getElementById(`export-${type === 'activity' ? 'activity-' : ''}logs-btn`);
    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Exporting...';

    fetch(buildUrlWithDri(`/avalara/export_logs?type=${type}`), {
      method: 'GET',
      headers: getAuthHeaders()
    })
    .then(response => {
      if (response.ok) {
        return response.blob();
      }
      throw new Error('Export failed');
    })
    .then(blob => {
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `avalara_${type}_logs_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showNotification('Logs exported successfully!', 'success');
    })
    .catch(error => {
      showNotification('Error exporting logs: ' + error.message, 'error');
    })
    .finally(() => {
      btn.disabled = false;
      btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg>Export';
    });
  }

  // Clear Logs function
  function clearLogs(type) {
    if (!confirm(`Are you sure you want to clear all ${type === 'activity' ? 'activity' : 'tax calculation'} logs? This action cannot be undone.`)) {
      return;
    }

    const btn = document.getElementById(`clear-${type === 'activity' ? 'activity-' : ''}logs-btn`);
    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Clearing...';

    fetch(buildUrlWithDri(`/avalara/clear_logs`), {
      method: 'DELETE',
      headers: getAuthHeaders(),
      body: JSON.stringify({ type: type })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification(`${data.count} logs cleared successfully!`, 'success');
        // Refresh the appropriate logs
        if (type === 'activity') {
          loadActivityLogs();
        } else {
          loadLogs();
        }
      } else {
        showNotification('Error clearing logs: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showNotification('Error clearing logs: ' + error.message, 'error');
    })
    .finally(() => {
      btn.disabled = false;
      btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>Clear';
    });
  }

  // Helper function to format timestamps
  function formatTimestamp(timestamp) {
    if (!timestamp) return 'N/A';

    try {
      const date = new Date(timestamp);
      if (isNaN(date.getTime())) return 'Invalid Date';

      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      });
    } catch (e) {
      console.error('Error formatting timestamp:', e);
      return 'Invalid Date';
    }
  }

  // showNotification and loadProductBlacklist functions are now defined globally above

  function addToBlacklist() {
    const input = document.getElementById('product-ids-input');
    const btn = document.getElementById('add-to-blacklist-btn');
    const productIds = input.value.trim();

    if (!productIds) {
      showNotification('Please enter at least one product ID', 'error');
      return;
    }

    // Parse input - handle both comma-separated and newline-separated
    const ids = productIds.split(/[,\n]/).map(id => id.trim()).filter(id => id.length > 0);

    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Adding...';

    fetch(buildUrlWithDri('/avalara/product_blacklist/add'), {
      method: 'POST',
      headers: {
        ...getAuthHeaders(),
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ product_ids: ids })
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification(data.message, 'success');
        input.value = '';
        loadProductBlacklist(); // Refresh the list
      } else {
        showNotification('Error: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showNotification('Error adding to blacklist: ' + error.message, 'error');
    })
    .finally(() => {
      btn.disabled = false;
      btn.innerHTML = 'Add to Blacklist';
    });
  }



  function clearBlacklist() {
    if (!confirm('Are you sure you want to clear the entire blacklist? This action cannot be undone.')) {
      return;
    }

    const btn = document.getElementById('clear-blacklist-btn');
    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Clearing...';

    fetch(buildUrlWithDri('/avalara/product_blacklist/clear'), {
      method: 'DELETE',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification(data.message, 'success');
        loadProductBlacklist(); // Refresh the list
      } else {
        showNotification('Error: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showNotification('Error clearing blacklist: ' + error.message, 'error');
    })
    .finally(() => {
      btn.disabled = false;
      btn.innerHTML = 'Clear All';
    });
  }

  // Visual Product Selection Functions
  let selectedProducts = new Set();
  let currentProducts = [];
  let currentPage = 1;
  let totalPages = 1;
  let isLoading = false;

  function toggleMode() {
    const visualMode = document.getElementById('visual-selection-mode');
    const manualMode = document.getElementById('manual-entry-mode');
    const toggleBtn = document.getElementById('toggle-manual-mode');

    if (visualMode.classList.contains('hidden')) {
      // Switch to visual mode
      visualMode.classList.remove('hidden');
      manualMode.classList.add('hidden');
      toggleBtn.textContent = 'Switch to Manual Entry';
      loadProducts(); // Load products when switching to visual mode
    } else {
      // Switch to manual mode
      visualMode.classList.add('hidden');
      manualMode.classList.remove('hidden');
      toggleBtn.textContent = 'Switch to Visual Selection';
    }
  }

  function loadProducts(search = '', page = 1) {
    if (isLoading) return; // Prevent multiple simultaneous requests

    isLoading = true;
    const loadingEl = document.getElementById('products-loading');
    const listEl = document.getElementById('products-list');
    const emptyEl = document.getElementById('products-empty');

    // Show loading
    loadingEl.classList.remove('hidden');
    if (page === 1) {
      listEl.innerHTML = ''; // Clear list only for first page
    }
    emptyEl.classList.add('hidden');

    const params = new URLSearchParams({
      per_page: 50,
      page: page
    });

    if (search.trim()) {
      params.append('search', search.trim());
    }

    fetch(buildUrlWithDri(`/avalara/fluid_products?${params}`), {
      method: 'GET',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      isLoading = false;
      loadingEl.classList.add('hidden');

      if (data.success && data.products.length > 0) {
        currentProducts = data.products;
        currentPage = data.pagination.page;
        totalPages = data.pagination.total_pages;

        renderProducts(data.products);
        updatePagination(data.pagination);

        // Show pagination info
        const total = data.pagination.total;
        const showing = data.products.length;
        showNotification(`Showing ${showing} of ${total} products`, 'info');
      } else {
        currentProducts = [];
        emptyEl.classList.remove('hidden');
        if (search.trim()) {
          showNotification(`No products found matching "${search}"`, 'info');
        } else {
          showNotification('No products found', 'info');
        }
      }
    })
    .catch(error => {
      isLoading = false;
      loadingEl.classList.add('hidden');
      emptyEl.classList.remove('hidden');
      showNotification('Error loading products: ' + error.message, 'error');
    });
  }



  function renderProducts(products) {
    const listEl = document.getElementById('products-list');
    const countEl = document.getElementById('products-count');
    const countNumberEl = document.getElementById('products-count-number');

    // Update count
    countNumberEl.textContent = products.length;
    if (products.length > 0) {
      countEl.classList.remove('hidden');
    } else {
      countEl.classList.add('hidden');
    }

    listEl.innerHTML = products.map(product => {
      // Get product image with fallbacks
      const imageUrl = product.image_url || product.compressed_image_url || null;
      const imageHtml = imageUrl
        ? `<img src="${imageUrl}" alt="${product.name || 'Product'}" class="w-12 h-12 object-cover rounded-md border border-gray-200" onerror="this.style.display='none'">`
        : `<div class="w-12 h-12 bg-gray-100 rounded-md border border-gray-200 flex items-center justify-center">
             <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
               <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
             </svg>
           </div>`;

      return `
        <div class="p-3 hover:bg-gray-50 flex items-center space-x-3">
          <input type="checkbox"
                 id="product-${product.id}"
                 class="product-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                 data-product-id="${product.id}"
                 ${selectedProducts.has(product.id.toString()) ? 'checked' : ''}>
          ${imageHtml}
          <label for="product-${product.id}" class="flex-1 cursor-pointer">
            <div class="flex justify-between items-center">
              <div>
                <p class="text-sm font-medium text-gray-900">${product.name || 'Unnamed Product'}</p>
                <p class="text-xs text-gray-500">ID: ${product.id} ${product.sku ? '• SKU: ' + product.sku : ''}</p>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">$${product.price || '0.00'}</p>
                <p class="text-xs text-gray-500">${product.status || 'active'}</p>
              </div>
            </div>
          </label>
        </div>
      `;
    }).join('');

    // Add event listeners to checkboxes
    document.querySelectorAll('.product-checkbox').forEach(checkbox => {
      checkbox.addEventListener('change', handleProductSelection);
    });
  }

  function handleProductSelection(event) {
    const productId = event.target.dataset.productId;

    if (event.target.checked) {
      selectedProducts.add(productId);
    } else {
      selectedProducts.delete(productId);
    }

    updateSelectedCount();
  }

  function updateSelectedCount() {
    const countEl = document.getElementById('selected-count');
    const addBtn = document.getElementById('add-selected-btn');
    const count = selectedProducts.size;

    countEl.textContent = `${count} product${count !== 1 ? 's' : ''} selected`;
    addBtn.disabled = count === 0;
  }

  function addSelectedProducts() {
    if (selectedProducts.size === 0) return;

    const btn = document.getElementById('add-selected-btn');
    const productIds = Array.from(selectedProducts);

    btn.disabled = true;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Adding...';

    // Create URL with product IDs as query parameters
    const params = new URLSearchParams();
    productIds.forEach(id => {
      params.append('product_ids[]', id);
    });

    fetch(buildUrlWithDri(`/avalara/product_blacklist/add?${params}`), {
      method: 'POST',
      headers: getAuthHeaders()
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showNotification(data.message, 'success');
        selectedProducts.clear();
        updateSelectedCount();
        loadProductBlacklist(); // Refresh the blacklist
        // Uncheck all checkboxes
        document.querySelectorAll('.product-checkbox').forEach(cb => cb.checked = false);
      } else {
        showNotification('Error: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showNotification('Error adding products: ' + error.message, 'error');
    })
    .finally(() => {
      btn.disabled = false;
      btn.innerHTML = 'Add Selected to Blacklist';
    });
  }

  function searchProducts() {
    const searchInput = document.getElementById('product-search-input');
    const searchTerm = searchInput.value.trim();
    loadProducts(searchTerm, 1); // Always search from page 1
  }

  function updatePagination(pagination) {
    const paginationEl = document.getElementById('products-pagination');
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    const pageInfo = document.getElementById('page-info');

    if (pagination.total_pages > 1) {
      paginationEl.classList.remove('hidden');

      // Update page info
      pageInfo.textContent = `Page ${pagination.page} of ${pagination.total_pages}`;

      // Update buttons
      prevBtn.disabled = pagination.page <= 1;
      nextBtn.disabled = pagination.page >= pagination.total_pages;
    } else {
      paginationEl.classList.add('hidden');
    }
  }

  function goToPage(page) {
    const searchInput = document.getElementById('product-search-input');
    const searchTerm = searchInput.value.trim();
    loadProducts(searchTerm, page);
  }

  // Event listeners for blacklist functionality
  document.getElementById('add-to-blacklist-btn').addEventListener('click', addToBlacklist);
  document.getElementById('clear-blacklist-btn').addEventListener('click', clearBlacklist);
  document.getElementById('refresh-blacklist-btn').addEventListener('click', loadProductBlacklist);

  // Event listeners for visual selection
  document.getElementById('toggle-manual-mode').addEventListener('click', toggleMode);
  document.getElementById('search-products-btn').addEventListener('click', searchProducts);
  document.getElementById('add-selected-btn').addEventListener('click', addSelectedProducts);

  // Search on Enter key
  document.getElementById('product-search-input').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      searchProducts();
    }
  });

  // Real-time search as user types (with debounce)
  let searchTimeout;
  document.getElementById('product-search-input').addEventListener('input', function(e) {
    clearTimeout(searchTimeout);
    const searchTerm = e.target.value.trim();

    // Immediate search for empty (show initial products)
    if (searchTerm === '') {
      loadProducts('', 1);
      return;
    }

    // Debounced search for non-empty terms
    searchTimeout = setTimeout(() => {
      loadProducts(searchTerm, 1);
    }, 500); // 500ms debounce for API calls
  });

  // Pagination event listeners
  document.getElementById('prev-page').addEventListener('click', function() {
    if (currentPage > 1) {
      goToPage(currentPage - 1);
    }
  });

  document.getElementById('next-page').addEventListener('click', function() {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1);
    }
  });

  // Load products initially when blacklist tab is shown (use existing blacklistTab variable)
  // Note: blacklistTab is already declared above, so we just add the additional event listener
  blacklistTab.addEventListener('click', function() {
    // Small delay to ensure tab content is visible
    setTimeout(() => {
      if (!document.getElementById('visual-selection-mode').classList.contains('hidden')) {
        // Load initial products when tab is opened
        loadProducts('', 1);
      }
    }, 100);
  });

  // Handle form submission - update hidden field with current blacklist
  const form = document.querySelector('form');
  if (form) {
    form.addEventListener('submit', function(e) {
      // Update the hidden field with current blacklist
      const hiddenField = document.getElementById('product_blacklist_field');
      if (hiddenField) {
        hiddenField.value = JSON.stringify(productBlacklist);
      }
    });
  }
});
</script>
