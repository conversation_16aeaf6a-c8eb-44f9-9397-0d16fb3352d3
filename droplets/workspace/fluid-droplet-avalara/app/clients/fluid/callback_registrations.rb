# frozen_string_literal: true

module Fluid
  # Module for managing callback registrations through the Fluid API.
  #
  # This module provides full CRUD operations for callback registrations, allowing
  # droplets to register, update, and manage their callback endpoints with the Fluid API.
  # Callback registrations define the actual webhook URLs that Fluid will call when
  # specific events occur.
  #
  # @example Basic usage
  #   client = FluidClient.new
  #
  #   # Register a new callback
  #   registration = client.callback_registrations.create(
  #     definition_name: 'update_cart_tax',
  #     url: 'https://droplet.example.com/callbacks/tax',
  #     timeout_in_seconds: 30,
  #     active: true
  #   )
  #
  #   # Update the registration
  #   client.callback_registrations.update(registration['uuid'], active: false)
  #
  # <AUTHOR> Commerce Team
  # @since 1.0.0
  module CallbackRegistrations
    # Get the callback registrations resource instance.
    #
    # @return [Fluid::CallbackRegistrations::Resource] Resource instance for callback registrations
    def callback_registrations
      @callback_registrations ||= Resource.new(self)
    end

    # Resource class for callback registrations API operations.
    #
    # Provides full CRUD functionality for managing callback registrations
    # including creation, retrieval, updates, and deletion.
    class Resource
      # Initialize a new callback registrations resource.
      #
      # @param client [FluidClient] HTTP client instance for API communication
      def initialize(client)
        @client = client
      end

      # Retrieve callback registrations with optional filtering.
      #
      # @param params [Hash] Query parameters for filtering
      # @option params [Boolean] :active Filter by active status
      # @option params [Integer] :company_id Filter by company ID
      # @option params [String] :definition_name Filter by callback definition name
      # @option params [Integer] :page Page number for pagination
      # @option params [Integer] :per_page Number of results per page
      # @option params [String] :sorted_by Sort field
      #
      # @return [Array<Hash>] Array of callback registration objects
      #
      # @example
      #   # Get all active registrations
      #   active_registrations = client.callback_registrations.get(active: true)
      #
      #   # Get registrations for a specific definition
      #   tax_registrations = client.callback_registrations.get(
      #     definition_name: 'update_cart_tax'
      #   )
      def get(params = {})
        query_string = build_query_string(params)
        @client.get("/api/v1/callback_registrations#{query_string}")
      end

      # Create a new callback registration.
      #
      # @param attributes [Hash] Registration attributes
      # @option attributes [String] :definition_name Name of the callback definition
      # @option attributes [String] :url Webhook URL to call
      # @option attributes [Integer] :timeout_in_seconds Request timeout (default: 20)
      # @option attributes [Boolean] :active Whether the registration is active (default: true)
      #
      # @return [Hash] Created registration object with UUID
      #
      # @example
      #   registration = client.callback_registrations.create(
      #     definition_name: 'update_cart_shipping',
      #     url: 'https://droplet.example.com/callbacks/shipping',
      #     timeout_in_seconds: 15,
      #     active: true
      #   )
      #   puts "Created registration: #{registration['uuid']}"
      def create(attributes = {})
        @client.post("/api/callback/registrations", body: payload(attributes))
      end

      # Retrieve a specific callback registration by UUID.
      #
      # @param uuid [String] UUID of the callback registration
      #
      # @return [Hash] Callback registration object
      #
      # @example
      #   registration = client.callback_registrations.show('uuid-here')
      #   puts "URL: #{registration['url']}"
      def show(uuid)
        @client.get("/api/v1/callback_registrations/#{uuid}")
      end

      # Update an existing callback registration.
      #
      # @param uuid [String] UUID of the callback registration
      # @param attributes [Hash] Attributes to update
      # @option attributes [String] :url New webhook URL
      # @option attributes [Integer] :timeout_in_seconds New timeout value
      # @option attributes [Boolean] :active New active status
      #
      # @return [Hash] Updated registration object
      #
      # @example
      #   updated = client.callback_registrations.update('uuid-here', {
      #     url: 'https://new-url.example.com/callbacks/tax',
      #     active: false
      #   })
      def update(uuid, attributes = {})
        @client.put("/api/v1/callback_registrations/#{uuid}", body: payload(attributes, uuid))
      end

      # Delete a callback registration.
      #
      # @param uuid [String] UUID of the callback registration to delete
      #
      # @return [Hash] Deletion confirmation
      #
      # @example
      #   client.callback_registrations.delete('uuid-here')
      def delete(uuid)
        @client.delete("/api/v1/callback_registrations/#{uuid}")
      end

      # Build the API payload for create/update operations.
      #
      # @param attributes [Hash] Registration attributes
      # @param uuid [String, nil] UUID for update operations
      #
      # @return [Hash] Formatted payload for API request
      #
      # @api private
      def payload(attributes = {}, uuid = nil)
        payload_data = {
          "callback_registration" => {
            "definition_name" => attributes[:definition_name],
            "url" => attributes[:url],
            "timeout_in_seconds" => attributes.key?(:timeout_in_seconds) ? attributes[:timeout_in_seconds] : 20,
            "active" => attributes.key?(:active) ? attributes[:active] : true,
          },
        }

        payload_data["uuid"] = uuid if uuid

        payload_data
      end

    private

      # Build query string from parameters hash.
      #
      # @param params [Hash] Query parameters
      #
      # @return [String] Formatted query string with leading '?' or empty string
      #
      # @api private
      def build_query_string(params)
        return "" if params.empty?

        allowed_params = %i[active company_id definition_name page per_page sorted_by]

        query_string = params
          .filter_map { |key, value| "#{key}=#{value}" if allowed_params.include?(key) }
          .join("&")

        query_string.empty? ? "" : "?#{query_string}"
      end
    end
  end
end
