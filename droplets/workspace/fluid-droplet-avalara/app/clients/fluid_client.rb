# HTTP client for communicating with the Fluid Commerce API.
#
# This client provides a unified interface for all Fluid API operations including
# droplet management, webhook handling, callback definitions, and callback registrations.
# It includes comprehensive error handling, response processing, and authentication
# management.
#
# The client supports:
# - RESTful HTTP operations (GET, POST, PUT, DELETE)
# - Automatic authentication using API keys
# - Response parsing and error handling
# - Modular functionality through included modules
# - Comprehensive error classification and handling
#
# @example Basic usage
#   client = FluidClient.new(company.authentication_token)
#   response = client.get('/api/v1/companies/current')
#   puts response['name']
#
# @example Callback registration
#   client = FluidClient.new(company.authentication_token)
#   result = client.callback_registrations.create({
#     definition_name: 'update_cart_tax',
#     url: 'https://droplet.example.com/callbacks/tax',
#     active: true
#   })
#
# <AUTHOR> Commerce Team
# @since 1.0.0
class FluidClient
  include HTTParty
  include Fluid::Droplets
  include Fluid::Webhooks
  include Fluid::CallbackDefinitions
  include Fluid::CallbackRegistrations

  base_uri Setting.fluid_api.base_url
  format :json

  Error                 = Class.new(StandardError)
  AuthenticationError   = Class.new(Error)
  ResourceNotFoundError = Class.new(Error)
  APIError              = Class.new(Error)

  def initialize(auth_token = nil, company: nil)
    @http = self.class
    @company = company

    # Get API configuration from settings (like bundles and catalog droplets)
    api_setting = Setting.fluid_api

    # Strategy: Use global admin token + company_id in headers
    # This is more robust than per-company tokens
    @auth_token = auth_token

    if api_setting&.values.present?
      config = api_setting.values
      @auth_token ||= config["api_key"]
    end

    @auth_token ||= ENV["FLUID_API_TOKEN"]

    # Fallback to company token if no global token available
    @auth_token ||= @company&.authentication_token if @company

    Rails.logger.info("FluidClient: Initialized with token: #{@auth_token&.first(10)}...")
    Rails.logger.info("FluidClient: Company context: #{@company&.name} (ID: #{@company&.fluid_company_id})") if @company

    update_headers

    Rails.logger.info("FluidClient: Headers: #{default_headers.except('Authorization').merge('Authorization' => 'Bearer [REDACTED]')}")
  end

  # Perform a GET request to the Fluid API.
  #
  # @param path [String] API endpoint path
  # @param options [Hash] Request options (query parameters, headers, etc.)
  #
  # @return [Hash] Parsed response data
  # @raise [FluidClient::APIError] When the request fails
  def get(path, options = {})
    handle_response(@http.get(path, format_options(options)))
  end

  # Perform a POST request to the Fluid API.
  #
  # @param path [String] API endpoint path
  # @param options [Hash] Request options (body, headers, etc.)
  #
  # @return [Hash] Parsed response data
  # @raise [FluidClient::APIError] When the request fails
  def post(path, options = {})
    handle_response(@http.post(path, format_options(options)))
  end

  # Perform a PUT request to the Fluid API.
  #
  # @param path [String] API endpoint path
  # @param options [Hash] Request options (body, headers, etc.)
  #
  # @return [Hash] Parsed response data
  # @raise [FluidClient::APIError] When the request fails
  def put(path, options = {})
    handle_response(@http.put(path, format_options(options)))
  end

  def delete(path, options = {})
    handle_response(@http.delete(path, format_options(options)))
  end

  # Get products from Fluid API for product blacklist interface
  #
  # @param params [Hash] Query parameters (page, per_page, search, etc.)
  # @return [Hash] Response with products array and pagination info
  def list_products(params = {})
    default_params = {
      per_page: 50,
      page: 1
    }

    # Add company_id to query params if company context is available
    if @company&.fluid_company_id.present?
      default_params[:company_id] = @company.fluid_company_id
    end

    Rails.logger.info("FluidClient: list_products with params: #{default_params.merge(params)}")
    get("/api/company/v1/products", query: default_params.merge(params))
  end

private

  def update_headers
    self.class.headers default_headers
  end

  def format_options(options)
    options[:body] = options[:body].to_json if options[:body].is_a?(Hash)
    options[:headers] = default_headers.merge(options[:headers] || {})

    options
  end

  def default_headers
    headers = {
      "Authorization" => "Bearer #{@auth_token}",
      "Content-Type" => "application/json",
    }

    # Add company_id header if company context is available
    if @company&.fluid_company_id.present?
      headers["X-Company-ID"] = @company.fluid_company_id.to_s
    end

    headers
  end

  def handle_response(response)
    case response.code
    when 200..299
      response.parsed_response
    when 401
      raise AuthenticationError, response
    when 404
      raise ResourceNotFoundError, response
    else
      raise APIError, response
    end
  end
end
