# frozen_string_literal: true

class AvalaraSettingsController < ApplicationController
  # Skip CSRF for iframe usage - droplet is loaded in iframe from Fluid
  skip_before_action :verify_authenticity_token

  before_action :authenticate_dri
  before_action :set_company_from_dri
  before_action :set_integration_setting, only: %i[show create update test_connection product_blacklist add_to_blacklist remove_from_blacklist clear_blacklist]
  skip_before_action :authenticate_dri, only: [:copy_credentials, :debug_credentials]

  # GET /avalara/configure
  def show
    # Debug: Log all headers for troubleshooting
    Rails.logger.info "=== AVALARA SETTINGS SHOW DEBUG ==="
    Rails.logger.info "Authorization header: #{request.headers['Authorization']}"
    Rails.logger.info "X-Fluid-Company-Id header: #{request.headers['X-Fluid-Company-Id']}"
    Rails.logger.info "X-Fluid-User-Id header: #{request.headers['X-Fluid-User-Id']}"
    Rails.logger.info "All HTTP headers: #{request.headers.to_h.select { |k, v| k.start_with?('HTTP_') }}"
    Rails.logger.info "=================================="

    # @integration_setting is already set by set_integration_setting before_action
    # Only create new one if it doesn't exist
    @integration_setting ||= IntegrationSetting.new(company_id: @company_id)

    # TEMPORARY FIX: Copy credentials to the checkout company ID (*********)
    if @integration_setting&.persisted? && @integration_setting.account_id.present?
      checkout_company_id = *********
      checkout_setting = IntegrationSetting.find_or_initialize_by(company_id: checkout_company_id)

      if checkout_setting.new_record? || !checkout_setting.account_id.present?
        Rails.logger.info "🔧 [AvalaraSettings] COPYING CREDENTIALS TO CHECKOUT COMPANY #{checkout_company_id}"
        checkout_setting.assign_attributes(
          enabled: @integration_setting.enabled,
          account_id: @integration_setting.account_id,
          license_key: @integration_setting.license_key,
          company_code: @integration_setting.company_code
        )
        checkout_setting.save!
        Rails.logger.info "🔧 [AvalaraSettings] CREDENTIALS COPIED TO CHECKOUT COMPANY SUCCESSFULLY"
      else
        Rails.logger.info "🔧 [AvalaraSettings] CHECKOUT COMPANY #{checkout_company_id} ALREADY HAS CREDENTIALS"
      end
    end

    # TEMPORARY FIX: Copy credentials to the other company ID used in checkout
    if @integration_setting&.persisted?
      other_company_id = *********
      other_setting = IntegrationSetting.find_or_initialize_by(company_id: other_company_id)

      if other_setting.new_record? || !other_setting.account_id.present?
        Rails.logger.info "🔧 [AvalaraSettings] COPYING CREDENTIALS TO COMPANY #{other_company_id}"
        other_setting.assign_attributes(
          enabled: @integration_setting.enabled,
          account_id: @integration_setting.account_id,
          license_key: @integration_setting.license_key,
          company_code: @integration_setting.company_code
        )
        other_setting.save!
        Rails.logger.info "🔧 [AvalaraSettings] CREDENTIALS COPIED SUCCESSFULLY"
      end
    end

    # Debug: Show what we're loading
    Rails.logger.info "🔧 [AvalaraSettings] SHOW DEBUG:"
    Rails.logger.info "  - @company_id: #{@company_id}"
    Rails.logger.info "  - @integration_setting.id: #{@integration_setting.id}"
    Rails.logger.info "  - @integration_setting.new_record?: #{@integration_setting.new_record?}"
    Rails.logger.info "  - @integration_setting.enabled: #{@integration_setting.enabled}"
    Rails.logger.info "  - @integration_setting.account_id: #{@integration_setting.account_id&.first(3)}***"
    Rails.logger.info "  - @integration_setting.license_key: #{@integration_setting.license_key&.first(3)}***"
    Rails.logger.info "  - @integration_setting.company_code: #{@integration_setting.company_code&.first(3)}***"

    @callback_status = get_callback_status if @company_id
  end

  # POST /avalara/configure
  def create
    Rails.logger.info "🔧 [AvalaraSettings] CREATE DEBUG:"
    Rails.logger.info "  - @company_id: #{@company_id}"
    Rails.logger.info "  - integration_setting_params: #{integration_setting_params.inspect}"

    # Find existing integration setting or create new one
    @integration_setting = IntegrationSetting.find_by(company_id: @company_id) ||
                          IntegrationSetting.new(company_id: @company_id)

    Rails.logger.info "  - @integration_setting.new_record?: #{@integration_setting.new_record?}"
    Rails.logger.info "  - @integration_setting.id: #{@integration_setting.id}"

    # Update attributes
    @integration_setting.assign_attributes(integration_setting_params)

    Rails.logger.info "  - After assign_attributes:"
    Rails.logger.info "    - enabled: #{@integration_setting.enabled}"
    Rails.logger.info "    - account_id: #{@integration_setting.account_id&.first(3)}***"
    Rails.logger.info "    - license_key: #{@integration_setting.license_key&.first(3)}***"
    Rails.logger.info "    - company_code: #{@integration_setting.company_code&.first(3)}***"

    if @integration_setting.save
      Rails.logger.info "  - ✅ SAVE SUCCESSFUL"
      flash[:success] = "Avalara configuration saved successfully!"
      redirect_to avalara_configure_path(dri: params[:dri])
    else
      Rails.logger.error "  - ❌ SAVE FAILED"
      Rails.logger.error "  - Errors: #{@integration_setting.errors.full_messages}"
      Rails.logger.error "  - Valid?: #{@integration_setting.valid?}"
      flash.now[:error] = "Failed to save configuration: #{@integration_setting.errors.full_messages.join(', ')}"
      render :show
    end
  end

  # PATCH /avalara/configure
  def update
    # Find existing integration setting or create new one
    @integration_setting = IntegrationSetting.find_by(company_id: @company_id) ||
                          IntegrationSetting.new(company_id: @company_id)

    # Filter out asterisks from sensitive fields - don't update if user didn't change them
    filtered_params = integration_setting_params.dup
    filtered_params.delete(:account_id) if filtered_params[:account_id] == "****"
    filtered_params.delete(:license_key) if filtered_params[:license_key] == "****"
    filtered_params.delete(:company_code) if filtered_params[:company_code] == "****"

    # Use assign_attributes + save for both new and existing records
    @integration_setting.assign_attributes(filtered_params)

    if @integration_setting.save
      ActivityLog.log_success(
        company_id: @company_id,
        activity_type: "configuration",
        message: "Avalara configuration updated successfully",
        details: "Enabled: #{@integration_setting.enabled?}",
        metadata: {
          enabled: @integration_setting.enabled?,
          has_credentials: @integration_setting.credentials_valid?,
        },
        request: request
      )
      flash[:success] = "Avalara configuration updated successfully!"
      redirect_to avalara_configure_path(dri: params[:dri])
    else
      ActivityLog.log_error(
        company_id: @company_id,
        activity_type: "configuration",
        message: "Failed to update Avalara configuration",
        details: @integration_setting.errors.full_messages.join(", "),
        metadata: { errors: @integration_setting.errors.full_messages },
        request: request
      )
      flash.now[:error] = "Failed to update configuration: #{@integration_setting.errors.full_messages.join(', ')}"
      render :show
    end
  end

  # POST /avalara/test_connection
  def test_connection
    if @integration_setting.blank? || !@integration_setting.credentials_valid?
      ActivityLog.log_error(
        company_id: @company_id,
        activity_type: "test_connection",
        message: "Test connection failed: credentials not configured",
        details: "Please configure your Avalara credentials first.",
        request: request
      )
      render json: { success: false, message: "Please configure your Avalara credentials first." }
      return
    end

    # Test the connection using the configured credentials
    result = test_avalara_connection(@integration_setting)

    if result[:success]
      @integration_setting.touch(:last_used_at)
      ActivityLog.log_success(
        company_id: @company_id,
        activity_type: "test_connection",
        message: "Avalara connection test successful",
        details: result[:message],
        request: request
      )
      render json: { success: true, message: "Connection successful!" }
    else
      ActivityLog.log_error(
        company_id: @company_id,
        activity_type: "test_connection",
        message: "Avalara connection test failed",
        details: result[:error],
        request: request
      )
      render json: { success: false, message: result[:error] }
    end
  end

  # POST /avalara/callbacks/register
  def register_callbacks
    handle_callback_operation(:register_all_callbacks, "registered")
  end

  # DELETE /avalara/callbacks/unregister
  def unregister_callbacks
    handle_callback_operation(:unregister_all_callbacks, "unregistered")
  end

  # GET /avalara/callbacks/status
  def callback_status
    render json: get_callback_status
  end

  # GET /avalara/logs
  def logs
    # Debug: Log what we're looking for
    Rails.logger.info "🔍 [LOGS] DEBUG:"
    Rails.logger.info "  - @company_id: #{@company_id}"
    Rails.logger.info "  - All TaxCalculationLog company_ids: #{TaxCalculationLog.distinct.pluck(:company_id)}"
    Rails.logger.info "  - Total logs in DB: #{TaxCalculationLog.count}"
    Rails.logger.info "  - Logs for this company: #{TaxCalculationLog.for_company(@company_id).count}"

    # IMPORTANT: Also check for logs with the checkout company_id (*********)
    # because callbacks save logs with the company_id from the cart payload
    checkout_company_id = *********
    Rails.logger.info "  - Logs for checkout company (#{checkout_company_id}): #{TaxCalculationLog.for_company(checkout_company_id).count}"

    # Get logs for the last 24 hours by default
    since = params[:since]&.to_i&.hours&.ago || 24.hours.ago

    # Search in both company IDs to catch logs from both sources
    logs = TaxCalculationLog.where(company_id: [@company_id, checkout_company_id])
                           .where(created_at: since..)
                           .recent
                           .limit(50)

    # Get stats from both company IDs
    config_stats = TaxCalculationLog.stats_for_company(@company_id, since: since)
    checkout_stats = TaxCalculationLog.stats_for_company(checkout_company_id, since: since)

    # Combine stats
    stats = {
      total: config_stats[:total] + checkout_stats[:total],
      successful: config_stats[:successful] + checkout_stats[:successful],
      failed: config_stats[:failed] + checkout_stats[:failed],
      success_rate: logs.count > 0 ? ((config_stats[:successful] + checkout_stats[:successful]).to_f / logs.count * 100).round(1) : 0,
      avg_response_time: [config_stats[:avg_response_time], checkout_stats[:avg_response_time]].max
    }

    Rails.logger.info "  - Combined logs found: #{logs.count}"
    Rails.logger.info "  - Combined stats: #{stats}"

    render json: {
      logs: logs.map do |log|
        {
          id: log.id,
          timestamp: log.created_at.iso8601,
          cart_id: log.cart_id,
          status: log.status,
          tax_total: log.formatted_tax_total,
          response_time: log.formatted_response_time,
          location: log.ship_to_location,
          items_count: log.items_count,
          error_message: log.error_message,
          # Detailed data for expand
          request_payload: log.request_payload,
          response_payload: log.response_payload,
          avalara_request: log.avalara_request,
          avalara_response: log.avalara_response,
        }
      end,
      stats: stats,
    }
  end

  # GET /avalara/activity_logs
  def activity_logs
    # Debug: Log all headers for troubleshooting AJAX calls
    Rails.logger.info "=== ACTIVITY LOGS AJAX DEBUG ==="
    Rails.logger.info "Authorization header: #{request.headers['Authorization']}"
    Rails.logger.info "X-Fluid-Company-Id header: #{request.headers['X-Fluid-Company-Id']}"
    Rails.logger.info "X-Fluid-User-Id header: #{request.headers['X-Fluid-User-Id']}"
    Rails.logger.info "Company ID from before_action: #{@company_id}"
    Rails.logger.info "All HTTP headers: #{request.headers.to_h.select { |k, v| k.start_with?('HTTP_') }}"
    Rails.logger.info "=================================="

    # Get activity logs for the company
    since = params[:since]&.to_i&.hours&.ago || 24.hours.ago

    logs = ActivityLog.for_company(@company_id)
                     .where(created_at: since..)
                     .recent
                     .limit(50)

    # Get stats
    stats = ActivityLog.stats_for_company(@company_id, since: since)

    render json: {
      logs: logs.map do |log|
        {
          id: log.id,
          timestamp: log.created_at.iso8601,
          activity_type: log.activity_type,
          status: log.status,
          message: log.message,
          details: log.details,
          ip_address: log.ip_address,
          user_agent: log.user_agent
        }
      end,
      stats: stats
    }
  end

  # DELETE /avalara/clear_logs
  def clear_logs
    log_type = params[:type] || 'tax_calculation'

    Rails.logger.info "🗑️ [CLEAR_LOGS] Clearing #{log_type} logs for company #{@company_id}"

    begin
      count = case log_type
              when 'activity'
                ActivityLog.for_company(@company_id).delete_all
              else
                # Clear from both company IDs since we search in both
                checkout_company_id = *********
                config_count = TaxCalculationLog.for_company(@company_id).delete_all
                checkout_count = TaxCalculationLog.for_company(checkout_company_id).delete_all
                config_count + checkout_count
              end

      Rails.logger.info "🗑️ [CLEAR_LOGS] Cleared #{count} #{log_type} logs"

      render json: {
        success: true,
        count: count,
        message: "#{count} #{log_type.humanize} logs cleared successfully"
      }
    rescue => e
      Rails.logger.error "🗑️ [CLEAR_LOGS] Error: #{e.message}"
      render json: {
        success: false,
        message: "Error clearing logs: #{e.message}"
      }, status: :internal_server_error
    end
  end

  # POST /avalara/sync_status
  def sync_status
    begin
      # Check if we can connect to Avalara
      integration_setting = IntegrationSetting.find_by(company_id: @company.fluid_company_id)

      if integration_setting&.enabled?
        ActivityLog.log_success(
          company_id: @company_id,
          activity_type: "sync_status",
          message: "Sync status check successful",
          details: "Avalara integration is enabled and working",
          metadata: {
            integration_enabled: true,
            company_id: @company.fluid_company_id
          },
          request: request
        )

        render json: {
          success: true,
          message: "Sync successful - Avalara connection is working",
          timestamp: Time.current.iso8601,
          company_id: @company.fluid_company_id
        }
      else
        ActivityLog.log_warning(
          company_id: @company_id,
          activity_type: "sync_status",
          message: "Sync status check: integration not enabled",
          details: "Integration is not enabled for this company",
          metadata: {
            integration_enabled: false,
            company_id: @company.fluid_company_id
          },
          request: request
        )

        render json: {
          success: false,
          message: "Integration is not enabled for this company",
          timestamp: Time.current.iso8601,
          company_id: @company.fluid_company_id
        }
      end
    rescue => e
      Rails.logger.error "🔄 [AvalaraSettings] Sync status error: #{e.message}"

      ActivityLog.log_error(
        company_id: @company_id,
        activity_type: "sync_status",
        message: "Sync status check failed",
        details: e.message,
        metadata: { error_class: e.class.name },
        request: request
      )

      render json: {
        success: false,
        message: "Sync failed: #{e.message}",
        timestamp: Time.current.iso8601
      }
    end
  end

  # POST /avalara/test_callback
  def test_callback
    begin
      ActivityLog.log_success(
        company_id: @company_id,
        activity_type: "test_callback",
        message: "Test callback executed successfully",
        details: "Callback test completed without errors",
        metadata: {
          dri: @company.droplet_installation_uuid,
          company_name: @company.name,
          fluid_shop: @company.fluid_shop
        },
        request: request
      )

      render json: {
        success: true,
        message: "Test callback successful",
        timestamp: Time.current.iso8601,
        company_id: @company.fluid_company_id,
        test_data: {
          dri: @company.droplet_installation_uuid,
          company_name: @company.name,
          fluid_shop: @company.fluid_shop
        }
      }
    rescue => e
      Rails.logger.error "🧪 [AvalaraSettings] Test callback error: #{e.message}"

      ActivityLog.log_error(
        company_id: @company_id,
        activity_type: "test_callback",
        message: "Test callback failed",
        details: e.message,
        metadata: { error_class: e.class.name },
        request: request
      )

      render json: {
        success: false,
        message: "Test callback failed: #{e.message}",
        timestamp: Time.current.iso8601
      }
    end
  end

  # GET /avalara/product_blacklist
  def product_blacklist
    Rails.logger.info "🔍 [AvalaraSettings] PRODUCT_BLACKLIST DEBUG:"
    Rails.logger.info "  - @company present: #{@company.present?}"
    Rails.logger.info "  - @company_id: #{@company_id}"
    Rails.logger.info "  - @integration_setting present: #{@integration_setting.present?}"
    Rails.logger.info "  - @integration_setting.id: #{@integration_setting&.id}"
    Rails.logger.info "  - session[:droplet_installation_uuid]: #{session[:droplet_installation_uuid]}"
    Rails.logger.info "  - params[:dri]: #{params[:dri]}"

    # If no integration setting, try to find or create one
    if @integration_setting.nil? && @company_id.present?
      Rails.logger.info "  - Trying to find/create IntegrationSetting for company_id: #{@company_id}"
      @integration_setting = IntegrationSetting.find_or_create_by(company_id: @company_id) do |setting|
        setting.enabled = false
      end
      Rails.logger.info "  - Found/created IntegrationSetting: #{@integration_setting.id}"
    end

    # Force reload from database to avoid cache issues
    @integration_setting&.reload
    Rails.logger.info "  - After reload - @integration_setting.product_blacklist: #{@integration_setting&.product_blacklist}"
    Rails.logger.info "  - After reload - @integration_setting.blacklist_count: #{@integration_setting&.blacklist_count}"

    blacklist = @integration_setting&.product_blacklist || []
    count = @integration_setting&.blacklist_count || 0

    Rails.logger.info "  - Final blacklist: #{blacklist}"
    Rails.logger.info "  - Final count: #{count}"

    render json: {
      success: true,
      blacklist: blacklist,
      count: count
    }
  end

  # GET /avalara/fluid_products
  def fluid_products
    begin
      Rails.logger.info "🔍 [AvalaraSettings] FLUID_PRODUCTS DEBUG:"
      Rails.logger.info "  - @company present: #{@company.present?}"
      Rails.logger.info "  - @company.authentication_token present: #{@company&.authentication_token.present?}"
      Rails.logger.info "  - @company.authentication_token (first 10 chars): #{@company&.authentication_token&.first(10)}..."
      Rails.logger.info "  - @company.fluid_company_id: #{@company&.fluid_company_id}"
      Rails.logger.info "  - @company.name: #{@company&.name}"

      # Validate that we have a valid authentication token
      if @company.blank? || @company.authentication_token.blank?
        Rails.logger.error "🔍 [AvalaraSettings] No company or authentication token found"
        render json: {
          success: false,
          message: "Company authentication not found",
          products: []
        }
        return
      end

      client = FluidClient.new(nil, company: @company)

      # Optimized for 2800+ products: search against API instead of loading all
      search_params = {
        page: params[:page] || 1,
        per_page: params[:per_page] || 50  # Reasonable page size
      }

      # Add search if provided (Fluid API expects 'search_query' parameter)
      search_params[:search_query] = params[:search] if params[:search].present?

      Rails.logger.info "🔍 [AvalaraSettings] Searching products with params: #{search_params}"

      response = client.list_products(search_params)

      # Extract products from response
      products = if response.is_a?(Hash)
                   response["products"] || response["data"] || []
                 else
                   []
                 end

      # Debug: Log first product structure to see available fields
      if products.first.present?
        Rails.logger.info "🔍 [AvalaraSettings] First product structure: #{products.first.keys}"
        Rails.logger.info "🔍 [AvalaraSettings] Image fields: #{products.first.select { |k, v| k.to_s.downcase.include?('image') }}"
      end

      # Format products for the interface
      formatted_products = products.map do |product|
        {
          id: product["id"] || product["product_id"],
          name: product["name"] || product["title"],
          price: product["price"] || product["base_price"],
          sku: product["sku"] || product["product_code"],
          status: product["status"] || "active",
          image_url: product["image_url"] || product["compressed_image_url"] || product["image"] || nil
        }
      end

      # Get pagination info
      pagination = response.dig("meta", "pagination") || {}
      total = pagination["total_count"] || pagination["total"] || formatted_products.length

      Rails.logger.info "🔍 [AvalaraSettings] Found #{formatted_products.length} products (#{total} total)"

      render json: {
        success: true,
        products: formatted_products,
        pagination: {
          page: search_params[:page].to_i,
          per_page: search_params[:per_page].to_i,
          total: total,
          total_pages: pagination["total_pages"] || 1
        }
      }

    rescue => e
      Rails.logger.error "🔍 [AvalaraSettings] Error fetching Fluid products: #{e.message}"

      render json: {
        success: false,
        message: "Failed to fetch products: #{e.message}",
        products: []
      }
    end
  end

  # POST /avalara/product_blacklist/add
  def add_to_blacklist
    Rails.logger.info "🚀🚀🚀 ADD_TO_BLACKLIST METHOD STARTED 🚀🚀🚀"
    Rails.logger.info "🔍 [AvalaraSettings] ADD_TO_BLACKLIST DEBUG:"
    Rails.logger.info "  - params[:product_ids]: #{params[:product_ids]}"
    Rails.logger.info "  - @integration_setting present: #{@integration_setting.present?}"
    Rails.logger.info "  - @integration_setting.id: #{@integration_setting&.id}"

    product_ids = params[:product_ids]

    if product_ids.blank?
      render json: { success: false, message: "Product IDs are required" }
      return
    end

    # Handle both single ID and array of IDs
    ids_to_add = product_ids.is_a?(Array) ? product_ids : [product_ids]
    added_count = 0
    errors = []

    Rails.logger.info "  - ids_to_add: #{ids_to_add}"

    ids_to_add.each do |product_id|
      Rails.logger.info "  - Adding product_id: #{product_id}"
      if @integration_setting.add_to_blacklist(product_id)
        added_count += 1
        Rails.logger.info "    ✅ Added successfully"
      else
        errors << "Product ID '#{product_id}' already in blacklist or invalid"
        Rails.logger.info "    ❌ Failed to add (already exists or invalid)"
      end
    end

    Rails.logger.info "  - Final added_count: #{added_count}"
    Rails.logger.info "  - Final errors: #{errors}"

    ActivityLog.log_success(
      company_id: @company_id,
      activity_type: "blacklist_update",
      message: "Added #{added_count} product(s) to blacklist",
      details: "Product IDs: #{ids_to_add.join(', ')}",
      metadata: { added_ids: ids_to_add, added_count: added_count },
      request: request
    )

    # Reload to ensure we have the latest data
    @integration_setting.reload
    Rails.logger.info "  - After save reload - blacklist: #{@integration_setting.product_blacklist}"
    Rails.logger.info "  - After save reload - count: #{@integration_setting.blacklist_count}"

    render json: {
      success: true,
      message: "#{added_count} product(s) added to blacklist",
      added_count: added_count,
      errors: errors,
      blacklist: @integration_setting.product_blacklist,
      count: @integration_setting.blacklist_count
    }
  end

  # DELETE /avalara/product_blacklist/remove
  def remove_from_blacklist
    product_ids = params[:product_ids]

    if product_ids.blank?
      render json: { success: false, message: "Product IDs are required" }
      return
    end

    # Handle both single ID and array of IDs
    ids_to_remove = product_ids.is_a?(Array) ? product_ids : [product_ids]
    removed_count = 0
    errors = []

    ids_to_remove.each do |product_id|
      if @integration_setting.remove_from_blacklist(product_id)
        removed_count += 1
      else
        errors << "Product ID '#{product_id}' not found in blacklist"
      end
    end

    # Save the changes to the database
    unless @integration_setting.save
      render json: { success: false, message: "Failed to save changes", errors: @integration_setting.errors.full_messages }
      return
    end

    ActivityLog.log_success(
      company_id: @company_id,
      activity_type: "blacklist_update",
      message: "Removed #{removed_count} product(s) from blacklist",
      details: "Product IDs: #{ids_to_remove.join(', ')}",
      metadata: { removed_ids: ids_to_remove, removed_count: removed_count },
      request: request
    )

    render json: {
      success: true,
      message: "#{removed_count} product(s) removed from blacklist",
      removed_count: removed_count,
      errors: errors,
      blacklist: @integration_setting.product_blacklist,
      count: @integration_setting.blacklist_count
    }
  end

  # DELETE /avalara/product_blacklist/clear
  def clear_blacklist
    old_count = @integration_setting.blacklist_count
    @integration_setting.clear_blacklist!

    ActivityLog.log_success(
      company_id: @company_id,
      activity_type: "blacklist_clear",
      message: "Cleared product blacklist",
      details: "Removed #{old_count} product(s) from blacklist",
      metadata: { cleared_count: old_count },
      request: request
    )

    render json: {
      success: true,
      message: "Blacklist cleared successfully",
      cleared_count: old_count,
      blacklist: [],
      count: 0
    }
  end

private



  def authenticate_dri
    Rails.logger.info "🚀🚀🚀 AUTHENTICATE_DRI CALLED FOR ACTION: #{action_name} 🚀🚀🚀"
    droplet_installation_uuid = params[:dri]

    # Debug logging
    Rails.logger.info "🔍 [AvalaraSettings] authenticate_dri DEBUG:"
    Rails.logger.info "  - params[:dri]: #{params[:dri]}"
    Rails.logger.info "  - session[:droplet_installation_uuid]: #{session[:droplet_installation_uuid]}"
    Rails.logger.info "  - request.referer: #{request.referer}"
    Rails.logger.info "  - all params: #{params.inspect}"

    # If no DRI but request comes from Fluid admin, allow it
    if droplet_installation_uuid.blank? && request.referer&.include?("fluid.app")
      Rails.logger.info "🔍 [AvalaraSettings] Request from Fluid admin without DRI - allowing"
      return
    end

    unless droplet_installation_uuid.present?
      Rails.logger.error "🚨 [AvalaraSettings] Authentication failed - no DRI found"
      render json: { error: "Authentication droplet_installation_uuid missing" }, status: :unauthorized
      return
    end

    session[:droplet_installation_uuid] = droplet_installation_uuid
    Rails.logger.info "DRI authenticated: #{droplet_installation_uuid}"
  end

  def set_company_from_dri
    Rails.logger.info "🔍 [AvalaraSettings] set_company_from_dri called for action: #{action_name}"
    dri = session[:droplet_installation_uuid] || params[:dri]

    if dri.present?
      @company = Company.find_by(droplet_installation_uuid: dri)
      if @company
        Rails.logger.info "🔍 [AvalaraSettings] Company found: #{@company.fluid_company_id}"
        Rails.logger.info "🔍 [AvalaraSettings] About to proceed to set_integration_setting..."
      else
        Rails.logger.error "🚨 [AvalaraSettings] Company not found for DRI: #{dri}"
      end
    else
      # If no DRI and request from Fluid admin, use fallback
      if request.referer&.include?("fluid.app")
        Rails.logger.info "🔍 [AvalaraSettings] No DRI from Fluid admin, using fallback"
        @company = Company.where(
          company_droplet_uuid: "drp_1qjzr9gr61rcs0uvwty6of6rr7yxgtufx",
          active: true
        ).order(:created_at).first
      end
    end

    if @company
      @company_id = @company.fluid_company_id
      Rails.logger.info "🔍 [AvalaraSettings] Company found: #{@company.fluid_company_id}"
    else
      Rails.logger.error "🔍 [AvalaraSettings] No company found for DRI: #{dri}"
      render json: { error: "Company not found" }, status: :unauthorized
    end
  end

  def extract_company_id_from_dri
    droplet_installation_uuid = session[:droplet_installation_uuid]

    Rails.logger.info "🔍 [AvalaraSettings] Extracting company_id from DRI"
    Rails.logger.info "🔍 [AvalaraSettings] Session DRI: #{droplet_installation_uuid}"
    Rails.logger.info "🔍 [AvalaraSettings] Params DRI: #{params[:dri]}"
    Rails.logger.info "🔍 [AvalaraSettings] All companies: #{Company.pluck(:droplet_installation_uuid, :fluid_company_id)}"

    if droplet_installation_uuid.present?
      company = Company.find_by(droplet_installation_uuid: droplet_installation_uuid)
      if company.present?
        Rails.logger.info "🔍 [AvalaraSettings] Company found via session DRI: #{company.fluid_company_id}"
        return company.fluid_company_id
      else
        Rails.logger.warn "🔍 [AvalaraSettings] No company found for session DRI: #{droplet_installation_uuid}"
      end
    end

    # Try params DRI as fallback
    if params[:dri].present?
      company = Company.find_by(droplet_installation_uuid: params[:dri])
      if company.present?
        Rails.logger.info "🔍 [AvalaraSettings] Company found via params DRI: #{company.fluid_company_id}"
        return company.fluid_company_id
      else
        Rails.logger.warn "🔍 [AvalaraSettings] No company found for params DRI: #{params[:dri]}"
      end
    end

    # If only one company exists, use it (common in single-tenant scenarios)
    if Company.count == 1
      company = Company.first
      Rails.logger.info "🔍 [AvalaraSettings] Using single company: #{company.fluid_company_id}"
      return company.fluid_company_id
    end

    # Fallback to original method for backward compatibility
    company_id = params[:company_id] || session[:company_id] || request.headers["X-Company-ID"]

    # If still no company_id found, use default fallback
    if company_id.blank?
      company_id = 123
      Rails.logger.info "🔍 [AvalaraSettings] Using default fallback company_id: #{company_id}"
    else
      Rails.logger.info "🔍 [AvalaraSettings] Using fallback company_id: #{company_id}"
    end

    company_id.to_i
  end

  def set_integration_setting
    Rails.logger.info "🚀🚀🚀 SET_INTEGRATION_SETTING CALLED 🚀🚀🚀"
    Rails.logger.info "🚀 @company present: #{@company.present?}"

    return unless @company

    @company_id = @company.fluid_company_id
    @integration_setting = IntegrationSetting.for_company(@company_id)

    # Create IntegrationSetting if it doesn't exist (needed for blacklist functionality)
    if @integration_setting.nil?
      Rails.logger.info "🚀 Creating new IntegrationSetting for company #{@company_id}"
      @integration_setting = IntegrationSetting.create!(
        company_id: @company_id,
        enabled: false  # Start disabled until credentials are configured
      )
    end

    Rails.logger.info "🚀🚀🚀 SET_INTEGRATION_SETTING RESULTS 🚀🚀🚀"
    Rails.logger.info "🚀 @company_id: #{@company_id}"
    Rails.logger.info "🚀 @integration_setting found: #{@integration_setting.present?}"
    Rails.logger.info "🚀 @integration_setting.id: #{@integration_setting&.id}"
    Rails.logger.info "🚀 @integration_setting.enabled: #{@integration_setting&.enabled}"
    Rails.logger.info "🚀🚀🚀 END SET_INTEGRATION_SETTING 🚀🚀🚀"
  end

  def integration_setting_params
    params.require(:integration_setting).permit(
      :account_id,
      :license_key,
      :company_code,
      :enabled,
      configuration: {},
      product_blacklist: []
    )
  end

  def test_avalara_connection(setting)
    begin
      # Simple validation test - just check if credentials are present
      if setting.account_id.present? && setting.license_key.present? && setting.company_code.present?
        { success: true, message: "Credentials validated successfully!" }
      else
        { success: false, error: "Missing required credentials" }
      end
    rescue => e
      Rails.logger.error("Avalara connection test failed: #{e.message}")
      { success: false, error: e.message }
    end
  end

  def get_callback_status
    # Temporarily disable callbacks functionality until DropletConfig is implemented
    { enabled: false, message: "Callbacks functionality temporarily disabled" }
  end

  def handle_callback_operation(operation, action_name)
    company = find_company_or_render_error
    return unless company

    callback_service = CallbackRegistrationService.new(company)
    results = callback_service.send(operation)

    render json: format_callback_results(results, action_name)
  end

  def find_company_or_render_error
    company = Company.find_by(fluid_company_id: @company_id)
    unless company
      render json: { success: false, message: "Company not found" }
      return nil
    end
    company
  end

  def format_callback_results(results, action_name)
    successful = results.count { |r| r[:status] == :success }
    failed = results.count { |r| r[:status] == :error }

    {
      success: failed == 0,
      message: "#{successful} callback(s) #{action_name}#{failed > 0 ? ", #{failed} failed" : ""}",
      results: results,
    }
  end

  def debug_credentials
    render json: {
      companies: [
        {
          company_id: **********,
          setting: IntegrationSetting.for_company(**********)&.slice(:account_id, :license_key, :company_code, :enabled)&.transform_values { |v| v.is_a?(String) && v.length > 8 ? "#{v[0..7]}..." : v }
        },
        {
          company_id: *********,
          setting: IntegrationSetting.for_company(*********)&.slice(:account_id, :license_key, :company_code, :enabled)&.transform_values { |v| v.is_a?(String) && v.length > 8 ? "#{v[0..7]}..." : v }
        }
      ]
    }
  end

  def copy_credentials
    Rails.logger.info "🚀 [AvalaraSettings] MANUAL COPY CREDENTIALS TRIGGERED"

    # Get source credentials from configuration company
    source_company_id = **********
    source_setting = IntegrationSetting.for_company(source_company_id)

    if source_setting.blank? || !source_setting.credentials_valid?
      render json: {
        success: false,
        message: "No valid source credentials found in company #{source_company_id}"
      }
      return
    end

    # Copy to checkout company
    target_company_id = *********
    target_setting = IntegrationSetting.find_or_initialize_by(company_id: target_company_id)

    Rails.logger.info "🚀 [AvalaraSettings] COPYING FROM #{source_company_id} TO #{target_company_id}"
    Rails.logger.info "🚀 [AvalaraSettings] SOURCE LICENSE KEY: #{source_setting.license_key&.first(8)}..."

    target_setting.assign_attributes(
      enabled: source_setting.enabled,
      account_id: source_setting.account_id,
      license_key: source_setting.license_key,
      company_code: source_setting.company_code
    )

    if target_setting.save
      Rails.logger.info "🚀 [AvalaraSettings] MANUAL COPY SUCCESSFUL"
      Rails.logger.info "🚀 [AvalaraSettings] TARGET LICENSE KEY: #{target_setting.license_key&.first(8)}..."

      render json: {
        success: true,
        message: "Credentials copied successfully from company #{source_company_id} to #{target_company_id}",
        source_key: "#{source_setting.license_key&.first(8)}...",
        target_key: "#{target_setting.license_key&.first(8)}..."
      }
    else
      Rails.logger.error "🚀 [AvalaraSettings] MANUAL COPY FAILED: #{target_setting.errors.full_messages}"
      render json: {
        success: false,
        message: "Failed to copy credentials: #{target_setting.errors.full_messages.join(', ')}"
      }
    end
  end
end
