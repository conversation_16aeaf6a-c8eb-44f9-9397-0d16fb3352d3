# frozen_string_literal: true

require_relative '../../../lib/avalara_test_data'

module Api
  module V1
    # AvalaraController provides comprehensive Avalara tax calculation services
    # This is the main API for direct Avalara integration, testing, and debugging
    class AvalaraController < ApplicationController
      # Skip CSRF protection for API endpoints
      skip_before_action :verify_authenticity_token

      # POST /api/v1/avalara/calculate_tax
      # Calculate tax for a given cart/order with full response details
      def calculate_tax
        # Accept both 'cart' and 'cart_data' for flexibility (like the working controller)
        cart_data = params[:cart_data]&.permit! || params[:cart]&.permit!

        # Get integration setting for blacklist filtering
        company_id = params[:company_id]
        integration_setting = company_id.present? ? IntegrationSetting.find_by(company_id: company_id) : nil

        result = CalculateTaxWithAvalara.call(
          cart_data: cart_data,
          customer_code: params[:customer_code],
          currency_code: params[:currency_code],
          is_cross_border: params[:is_cross_border],
          purchase_order_no: params[:purchase_order_no],
          avalara_credentials: extract_credentials,
          integration_setting: integration_setting
        )

        if result.success?
          render json: {
            success: true,
            data: result.tax_result,
            avalara_payload: result.avalara_payload,
            avalara_response: result.avalara_response,
          }, status: :ok
        else
          render json: {
            success: false,
            error: result.error,
          }, status: :unprocessable_entity
        end
      end

      # GET /api/v1/avalara/ping
      # Health check for Avalara API
      def ping
        client = build_avalara_client

        result = client.ping

        if result.success?
          render json: {
            success: true,
            message: "Avalara API is healthy",
            data: result.data,
          }, status: :ok
        else
          render json: {
            success: false,
            error: result.error,
          }, status: :service_unavailable
        end
      end

      # GET /api/v1/avalara/example
      # Returns an example cart structure for testing
      def example
        render json: {
          success: true,
          data: AvalaraTestData.example_cart_data,
        }
      end

      # POST /api/v1/avalara/test_auth
      # Test different authentication methods
      def test_auth
        credentials = extract_credentials

        # Test with current credentials
        result1 = test_auth_method(credentials[:username], credentials[:password], credentials[:base_url])

        # Test with sandbox
        sandbox_url = credentials[:base_url].gsub("rest.avatax.com", "sandbox-rest.avatax.com")
        result2 = test_auth_method(credentials[:username], credentials[:password], sandbox_url)

        render json: {
          success: true,
          production_test: result1,
          sandbox_test: result2,
          credentials_used: {
            username: credentials[:username],
            base_url: credentials[:base_url],
          },
        }
      end

      # GET /api/v1/avalara/cache/stats
      def cache_stats
        stats = TaxCalculationCache.stats

        render json: {
          success: true,
          data: {
            cache_enabled: AvalaraConfig.instance.caching_enabled?,
            statistics: stats.to_h,
            cache_store: Rails.cache.class.name,
          },
        }
      end

      # DELETE /api/v1/avalara/cache
      def clear_cache
        TaxCalculationCache.clear_all

        render json: {
          success: true,
          message: "Cache cleared successfully",
        }
      end

      # POST /api/v1/avalara/cache/warm
      def warm_cache
        payloads = params[:payloads] || [ AvalaraTestData.example_cart_data ]

        TaxCalculationCache.warm_cache(payloads) do |payload|
          client = build_avalara_client
          client.calculate_tax(payload)
        end

        render json: {
          success: true,
          message: "Cache warming completed",
          payloads_processed: payloads.size,
        }
      end

    private

      def extract_credentials
        # Extract from parameters or use defaults
        credentials = {
          username: params[:avalara_username],
          password: params[:avalara_password],
          base_url: params[:avalara_base_url],
        }.compact # Remove nil values

        # If no credentials provided, use defaults
        credentials.empty? ? default_credentials : credentials
      end

      def default_credentials
        # Use environment variables for credentials
        {
          username: ENV['AVALARA_DEFAULT_USERNAME'] || "2007309370",
          password: ENV['AVALARA_DEFAULT_PASSWORD'] || "2533BDD11FCBA864",
          base_url: ENV['AVALARA_BASE_URL'] || "https://sandbox-rest.avatax.com",
        }
      end

      def test_auth_method(username, password, base_url)
        # Test multiple endpoints
        results = {}

        # Test 1: Standard ping endpoint
        client = AvalaraClient.new(
          username: username,
          password: password,
          base_url: base_url
        )

        results[:ping] = client.ping

        # Test 2: Try direct connection to base URL
        begin
          conn = Faraday.new(url: base_url.gsub("/api/v2", ""))
          response = conn.get("/api/v2/utilities/ping") do |req|
            req.headers["Authorization"] = "Basic #{Base64.strict_encode64("#{username}:#{password}")}"
            req.headers["Content-Type"] = "application/json"
          end

          results[:direct_ping] = {
            status: response.status,
            body: response.body,
            success: response.status == 200,
          }
        rescue => e
          results[:direct_ping] = {
            error: e.message,
            success: false,
          }
        end

        {
          base_url: base_url,
          results: results,
        }
      end

      def build_avalara_client
        credentials = extract_credentials
        AvalaraClient.new(
          username: credentials[:username],
          password: credentials[:password],
          base_url: credentials[:base_url]
        )
      end
    end
  end
end
