# frozen_string_literal: true

# Model for storing Avalara integration settings per company.
#
# This model manages company-specific Avalara API credentials and configuration
# settings. It provides secure storage of sensitive data with encryption in
# production environments and includes validation and helper methods for
# credential management.
#
# The model supports:
# - Encrypted storage of sensitive credentials (production only)
# - Environment-specific configuration (sandbox/production)
# - Thread-safe current integration setting management
# - Credential validation and status checking
# - Activity logging for audit trails
#
# @example Create integration setting
#   setting = IntegrationSetting.create!(
#     company_id: 123,
#     account_id: '**********',
#     license_key: 'ABC123DEF456',
#     company_code: 'MYCOMPANY',
#     environment: 'sandbox',
#     enabled: true
#   )
#
# @example Use in thread context
#   IntegrationSetting.with_current_setting(setting) do
#     # All Avalara operations will use this setting
#     result = CalculateTaxWithAvalara.call(cart_data: cart)
#   end
#
# <AUTHOR> Commerce Team
# @since 1.0.0
class IntegrationSetting < ApplicationRecord
  # Associations
  # Note: ActivityLogs are associated by company_id, not integration_setting_id

  # Validations
  validates :company_id, presence: true, uniqueness: { case_sensitive: false }

  # Only validate credentials if enabled
  validates :account_id, presence: true, if: :enabled?
  validates :license_key, presence: true, if: :enabled?
  validates :company_code, presence: true, if: :enabled?

  # JSON attributes
  attribute :product_blacklist, :json, default: []

  # Validate product_blacklist format
  validate :validate_product_blacklist_format

  # Encryption for sensitive data (disabled in development and test for simplicity)
  unless Rails.env.development? || Rails.env.test?
    encrypts :account_id, deterministic: true
    encrypts :license_key, deterministic: true
    encrypts :company_code, deterministic: true
    encrypts :authentication_token, deterministic: true
  end

  # Scopes
  scope :enabled, -> { where(enabled: true) }
  scope :disabled, -> { where(enabled: false) }

  # Instance methods
  def base_url
    case environment
    when "production"
      "https://rest.avatax.com"
    else
      "https://sandbox-rest.avatax.com"
    end
  end

  def environment
    ENV.fetch("AVALARA_ENVIRONMENT", "sandbox")
  end

  def credentials_valid?
    account_id.present? && license_key.present? && company_code.present?
  end

  def activate!
    update!(enabled: true)
  end

  def deactivate!
    update!(enabled: false)
  end

  def summary
    "account_id: #{account_id&.first(3)}***, environment: #{environment}, enabled: #{enabled}"
  end

  # Product Blacklist methods
  def add_to_blacklist(product_id)
    return false if product_id.blank?

    product_id = product_id.to_s.strip
    current_list = product_blacklist || []

    unless current_list.include?(product_id)
      update!(product_blacklist: current_list + [product_id])
      true
    else
      false
    end
  end

  def remove_from_blacklist(product_id)
    return false if product_id.blank?

    product_id = product_id.to_s.strip
    current_list = product_blacklist || []

    if current_list.include?(product_id)
      update!(product_blacklist: current_list - [product_id])
      true
    else
      false
    end
  end

  def product_blacklisted?(product_id)
    return false if product_id.blank?

    product_id = product_id.to_s.strip
    (product_blacklist || []).include?(product_id)
  end

  def blacklist_count
    (product_blacklist || []).length
  end

  def clear_blacklist!
    update!(product_blacklist: [])
  end

  # Class methods
  def self.for_company(company_id)
    find_by(company_id: company_id)
  end

  def self.create_for_company(company_id, attributes = {})
    create!(attributes.merge(company_id: company_id))
  end

  # Thread-local storage helpers (similar to Exigo pattern)
  def self.current_integration_setting
    # Try to get from current thread (set by controllers/middleware)
    Thread.current[:current_integration_setting] ||
    # Try to get from request context if available
    (defined?(Current) && Current.respond_to?(:integration_setting) && Current.integration_setting) ||
    # Return nil if no context available
    nil
  end

  def self.with_integration_setting(setting)
    old_setting = Thread.current[:current_integration_setting]
    Thread.current[:current_integration_setting] = setting
    yield
  ensure
    Thread.current[:current_integration_setting] = old_setting
  end

  def self.set_current(setting)
    Thread.current[:current_integration_setting] = setting
  end

  def self.clear_current
    Thread.current[:current_integration_setting] = nil
  end

  # Global defaults management (replaces AvalaraSettings)
  def self.create_defaults
    create_avalara_credentials_setting
    create_avalara_configuration_setting
  end

  def self.remove_defaults
    Setting.where(name: %w[
      avalara_credentials
      avalara_configuration
    ]).delete_all
  end

private

  def validate_product_blacklist_format
    return if product_blacklist.blank?

    unless product_blacklist.is_a?(Array)
      errors.add(:product_blacklist, "must be an array")
      return
    end

    product_blacklist.each_with_index do |item, index|
      unless item.is_a?(String) || item.is_a?(Integer)
        errors.add(:product_blacklist, "item at index #{index} must be a string or integer")
      end
    end
  end

  def self.create_avalara_credentials_setting
    Setting.find_or_create_by!(name: "avalara_credentials") do |setting|
      setting.description = "Avalara API credentials for tax calculations"
      setting.schema = {
        type: "object",
        required: %w[ environment ],
        properties: {
          environment: {
            type: "string",
            enum: %w[ sandbox production ],
            default: "sandbox",
          },
          username: {
            type: "string",
            description: "Avalara username (alternative to account_id/license_key)",
          },
          password: {
            type: "string",
            description: "Avalara password (alternative to account_id/license_key)",
          },
          account_id: {
            type: "string",
            description: "Avalara account ID (alternative to username/password)",
          },
          license_key: {
            type: "string",
            description: "Avalara license key (alternative to username/password)",
          },
          company_code: {
            type: "string",
            description: "Default company code for transactions",
          },
        },
      }
      setting.values = {
        environment: "sandbox",
        username: "",
        password: "",
        account_id: "",
        license_key: "",
        company_code: "",
      }
    end
  end

  def self.create_avalara_configuration_setting
    Setting.find_or_create_by!(name: "avalara_configuration") do |setting|
      setting.description = "Avalara service configuration options"
      setting.schema = {
        type: "object",
        properties: {
          enabled: {
            type: "boolean",
            default: false,
            description: "Enable/disable Avalara tax calculations",
          },
          fallback_to_manual: {
            type: "boolean",
            default: true,
            description: "Fallback to manual calculation on API errors",
          },
          include_shipping_tax: {
            type: "boolean",
            default: true,
            description: "Include shipping tax in calculations",
          },
          shipping_tax_code: {
            type: "string",
            default: "FR030000",
            description: "Tax code for shipping charges",
          },
          debug_logging: {
            type: "boolean",
            default: false,
            description: "Enable debug logging for API calls",
          },
          timeout_seconds: {
            type: "integer",
            default: 30,
            minimum: 5,
            maximum: 120,
            description: "API request timeout in seconds",
          },
        },
      }
      setting.values = {
        enabled: false,
        fallback_to_manual: true,
        include_shipping_tax: true,
        shipping_tax_code: "FR030000",
        debug_logging: false,
        timeout_seconds: 30,
      }
    end
  end
end
