# frozen_string_literal: true

# Main interactor for calculating tax using Avalara
# Acts as an organizer that coordinates the tax calculation process
class CalculateTaxWithAvalara
  include Interactor

  # Expected context inputs:
  # - cart_data: Hash or JSON string with cart/order information
  # - customer_code: String (optional, defaults to "Guest")
  # - currency_code: String (optional, defaults to "USD")
  # - is_cross_border: Boolean (optional, defaults to false)
  # - avalara_credentials: Hash with username and password (optional, uses ENV vars if not provided)
  # - integration_setting: IntegrationSetting instance (optional, for blacklist filtering)

  def call
    validate_inputs
    filter_blacklisted_products
    handle_empty_cart_after_filter
    build_payload
    call_avalara_api
    process_response
  rescue StandardError => e
    Rails.logger.error("CalculateTaxWithAvalara Error: #{e.message}")
    context.fail!(error: e.message)
  end

  private

  def validate_inputs
    if context.cart_data.blank?
      context.fail!(error: "Cart data is required")
    end
  end

  def filter_blacklisted_products
    result = FilterBlacklistedProducts.call(
      cart_data: context.cart_data,
      integration_setting: context.integration_setting
    )

    if result.failure?
      context.fail!(error: result.error)
      return
    end

    # Update context with filtered data
    context.filtered_cart_data = result.filtered_cart_data
    context.blacklisted_products = result.blacklisted_products
    context.is_empty_after_filter = result.is_empty_after_filter

    Rails.logger.info("CalculateTaxWithAvalara: Filtered #{result.blacklisted_products.length} blacklisted products")
  end

  def handle_empty_cart_after_filter
    return unless context.is_empty_after_filter

    Rails.logger.info("CalculateTaxWithAvalara: Cart is empty after blacklist filtering, returning zero tax")

    # Return zero tax result without calling Avalara
    context.tax_result = {
      total_tax: 0.0,
      tax_amount: 0.0,
      lines: [],
      summary: [],
      messages: ["All products were filtered by blacklist - no tax calculation needed"]
    }

    # Mark as successful and stop further processing
    context.early_return = true
  end

  def build_payload
    return if context.early_return

    # Use filtered cart data instead of original cart data
    cart_data_to_use = context.filtered_cart_data || context.cart_data

    builder = AvalaraPayloadBuilder.new(
      cart_data: cart_data_to_use,
      customer_code: context.customer_code || "Guest",
      currency_code: context.currency_code || "USD",
      is_cross_border: context.is_cross_border || false,
      purchase_order_no: context.purchase_order_no,
      company_code: context.avalara_credentials[:company_code]
    )

    context.avalara_payload = builder.build
    Rails.logger.info("Built Avalara payload: #{context.avalara_payload}")
  end

  def call_avalara_api
    return if context.early_return

    credentials = context.avalara_credentials || default_credentials

    # Enable demo mode if credentials are missing or explicitly requested
    demo_mode = context.demo_mode || credentials[:username].blank? || credentials[:password].blank?

    client = AvalaraClient.new(
      username: credentials[:username],
      password: credentials[:password],
      base_url: credentials[:base_url]
    )

    context.avalara_response = client.calculate_tax(context.avalara_payload)
    context.demo_mode_used = demo_mode

    if demo_mode
      Rails.logger.info("DEMO MODE: Avalara API response: #{context.avalara_response}")
    else
      Rails.logger.info("Avalara API response: #{context.avalara_response}")
    end
  end

  def process_response
    return if context.early_return

    if context.avalara_response.success?
      extract_tax_information
      context.success = true
    else
      context.fail!(error: context.avalara_response.error)
    end
  end

  def extract_tax_information
    data = context.avalara_response.data
    
    context.tax_result = {
      total_tax: data["totalTax"] || 0,
      subtotal: data["totalAmount"] || 0,
      total_amount: (data["totalAmount"] || 0) + (data["totalTax"] || 0),
      tax_lines: extract_tax_lines(data),
      avalara_transaction_id: data["id"],
      avalara_code: data["code"]
    }

    # Handle cross-border specific information
    if context.is_cross_border
      extract_cross_border_info(data)
    end
  end

  def extract_tax_lines(data)
    return [] unless data["lines"].present?

    data["lines"].map do |line|
      {
        line_number: line["lineNumber"],
        tax_amount: line["tax"] || 0,
        taxable_amount: line["taxableAmount"] || 0,
        tax_code: line["taxCode"],
        description: line["description"]
      }
    end
  end

  def extract_cross_border_info(data)
    # Extract customs and duty information for cross-border transactions
    context.tax_result[:customs_info] = {
      duties: extract_duties(data),
      vat: extract_vat(data),
      customs_message: extract_customs_message(data)
    }
  end

  def extract_duties(data)
    # Extract duty information from tax details
    return 0 unless data["summary"].present?

    data["summary"].find { |s| s["taxType"] == "LandedCost" }&.dig("tax") || 0
  end

  def extract_vat(data)
    # Extract VAT information
    return 0 unless data["summary"].present?

    data["summary"].find { |s| s["taxType"] == "Output" }&.dig("tax") || 0
  end

  def extract_customs_message(data)
    # Extract any customs-related messages
    data["messages"]&.select { |m| m["summary"]&.include?("customs") }&.first&.dig("details") || ""
  end

  def default_credentials
    {
      username: ENV.fetch("AVALARA_USERNAME", ""),
      password: ENV.fetch("AVALARA_PASSWORD", ""),
      base_url: ENV.fetch("AVALARA_BASE_URL", "https://rest.avatax.com/api/v2")
    }
  end
end
