{"RSpec": {"coverage": {"/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/avalara_config.rb": {"lines": [null, null, 1, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 1, null, null, 1, 1, 1, null, 1, null, 1, 1, 1, 1, null, null, null, null, null, 1, 7, 7, null, null, null, null, 1, 7, null, null, null, null, 1, 1, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, 1, 0, null, null, null, null, 1, 2, 0, 0, null, null, null, null, 1, 0, null, null, null, null, 1, 0, null, null, null, null, 1, 0, null, null, null, null, 1, 1, null, null, null, null, 1, 0, null, null, null, null, 1, 0, null, null, null, null, 1, 0, null, null, null, null, 1, 0, null, null, null, null, 1, 0, null, null, null, null, 1, 0, null, null, null, 1, 0, 0, 0, 0, null, null, null, null, 1, 0, null, null, 1, null, null, null, 1, 1, null, null, 1, 1, 0, 0, null, 1, null, null, 0, null, null, null, null, 1, 1, 1, 0, null, null, 1, null, 0, null, null, null, null, 1, null, 0, 0, null, null, null, 0, null, null, null, null, null, null, null, null, null, 1, 0, 0, 0, null, null, null, 1, 1, 1, 1, null, null, null, 1, 1, 0, null, null, null, null, 1, 1, 1, 2, 0, null, null, null, null, null, 1, 1, 1, 4, 0, null, null, null, null, null, null, null, null, 1, 0, 0, 0, null, 0, null, null, null, null, null, null, 1, 1, null, 0, null, 1, null, null, null, null, null, 1, 0, null, null, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null, null, 1, 0, null, null, null, null, null, null], "branches": {"[:if, 0, 82, 4, 82, 52]": {"[:then, 1, 82, 4, 82, 15]": 2, "[:else, 2, 82, 4, 82, 52]": 0}, "[:if, 3, 83, 4, 83, 80]": {"[:then, 4, 83, 4, 83, 15]": 0, "[:else, 5, 83, 4, 83, 80]": 0}, "[:if, 6, 170, 4, 175, 7]": {"[:then, 7, 171, 6, 172, 41]": 0, "[:else, 8, 174, 6, 174, 17]": 1}, "[:unless, 9, 184, 4, 186, 7]": {"[:else, 10, 184, 4, 186, 7]": 1, "[:then, 11, 185, 6, 185, 78]": 0}, "[:if, 12, 197, 4, 207, 7]": {"[:then, 13, 198, 6, 198, 43]": 0, "[:else, 14, 201, 6, 206, 7]": 0}, "[:unless, 15, 227, 4, 229, 7]": {"[:else, 16, 227, 4, 229, 7]": 1, "[:then, 17, 228, 6, 228, 89]": 0}, "[:unless, 18, 236, 6, 238, 9]": {"[:else, 19, 236, 6, 238, 9]": 2, "[:then, 20, 237, 8, 237, 86]": 0}, "[:unless, 21, 246, 6, 248, 9]": {"[:else, 22, 246, 6, 248, 9]": 4, "[:then, 23, 247, 8, 247, 80]": 0}, "[:if, 24, 258, 6, 262, 9]": {"[:then, 25, 259, 8, 259, 36]": 0, "[:else, 26, 261, 8, 261, 15]": 0}, "[:case, 27, 269, 4, 274, 7]": {"[:when, 28, 271, 6, 271, 31]": 0, "[:else, 29, 273, 6, 273, 39]": 1}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/event_handler.rb": {"lines": [1, null, null, null, 1, null, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 0, null, 0, null, 0, 0, 0, null, null, null, null, null, null, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, null, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, 1, 2, 2, null, null, 1, null, null, null, 1, 2, null, null, null], "branches": {"[:unless, 0, 28, 6, 31, 9]": {"[:else, 1, 28, 6, 31, 9]": 0, "[:then, 2, 29, 8, 30, 20]": 0}, "[:if, 3, 38, 6, 48, 9]": {"[:then, 4, 39, 8, 40, 40]": 0, "[:else, 5, 41, 6, 48, 9]": 0}, "[:if, 6, 41, 6, 48, 9]": {"[:then, 7, 43, 8, 44, 42]": 0, "[:else, 8, 46, 8, 47, 20]": 0}, "[:if, 9, 76, 6, 76, 69]": {"[:then, 10, 76, 25, 76, 51]": 0, "[:else, 11, 76, 54, 76, 69]": 2}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/jobs/droplet_uninstalled_job.rb": {"lines": [1, 1, null, 1, 0, 0, null, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, null, null, null, 1, null, 1, 0, null, 0, null, 0, null, 0, null, 0, null, null, null, 0, null, null, 1, 0, null, 0, 0, 0, null, 0, null, null, null], "branches": {"[:if, 0, 8, 4, 19, 7]": {"[:then, 1, 9, 6, 16, 22]": 0, "[:else, 2, 18, 6, 18, 104]": 0}, "[:unless, 3, 25, 4, 25, 57]": {"[:else, 4, 25, 4, 25, 57]": 0, "[:then, 5, 25, 4, 25, 10]": 0}, "[:if, 6, 43, 4, 48, 7]": {"[:then, 7, 44, 6, 45, 34]": 0, "[:else, 8, 47, 6, 47, 119]": 0}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/jobs/webhook_event_job.rb": {"lines": [1, 1, null, null, 1, null, null, 1, null, 1, 1, 0, null, null, null, 1, 0, 0, 0, 0, 0, null, null, 0, null, 0, null, null, null, 0, null, null, null, 1, 0, null, null, null, 1, null, null, 1, 0, null, null, null, 1, 0, null, null, null, 1, 0, null, null, 1, null, 1, 0, 0, 0, 0, null, null, null, 1, 0, 0, null, 0, null, null], "branches": {"[:\"&.\", 0, 27, 57, 27, 69]": {"[:then, 1, 27, 57, 27, 69]": 0, "[:else, 2, 27, 57, 27, 69]": 0}, "[:\"&.\", 3, 24, 79, 24, 91]": {"[:then, 4, 24, 79, 24, 91]": 0, "[:else, 5, 24, 79, 24, 91]": 0}, "[:if, 6, 60, 4, 63, 7]": {"[:then, 7, 61, 6, 62, 86]": 0, "[:else, 8, 60, 4, 63, 7]": 0}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/jobs/application_job.rb": {"lines": [1, null, null, null, null, null, null], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/jobs/droplet_installed_job.rb": {"lines": [1, 1, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 0, 0, null, null, 0, 0, null, 0, null, null, 0, null, 0, 0, null, null, 0, null, 0, null, 0, null, null, null, 1, null, 1, 0, 0, null, null, 0, null, null, null, 0, 0, null, 0, null, null, 0, null, null, null, null, null, null, null, null, 0, 0, null, 0, null, null, 0, 0, null, 0, null, null, 0, null, null, 1, 0, null, null, null, null, 0, 0, null, null, null, null, 0, null, null, null, 0, null, null, null, null, 0, null, null, 1, null, 1, 0, 0, 0, null, 0, null, null, 0, null, null, null, null, null, 0, 0, 0, null, 0, null, null, null, null, 0, null, null, null, null, null, 0, 0, null, null, null, 0, 0, null, 0, 0, null, null, null, null, null, 0, null, null, null, null, 1, 0, null, null, null, null, null, null, null, null, null, null, null, null], "branches": {"[:if, 0, 29, 4, 38, 7]": {"[:then, 1, 30, 6, 35, 61]": 0, "[:else, 2, 37, 6, 37, 113]": 0}, "[:if, 3, 52, 4, 56, 7]": {"[:then, 4, 53, 6, 53, 105]": 0, "[:else, 5, 55, 6, 55, 74]": 0}, "[:if, 6, 73, 4, 77, 7]": {"[:then, 7, 74, 6, 74, 98]": 0, "[:else, 8, 76, 6, 76, 123]": 0}, "[:if, 9, 88, 4, 101, 7]": {"[:then, 10, 89, 6, 96, 7]": 0, "[:else, 11, 98, 6, 100, 7]": 0}, "[:if, 12, 123, 8, 129, 11]": {"[:then, 13, 124, 10, 124, 77]": 0, "[:else, 14, 126, 10, 128, 11]": 0}, "[:if, 15, 137, 4, 139, 7]": {"[:then, 16, 138, 6, 138, 68]": 0, "[:else, 17, 137, 4, 139, 7]": 0}, "[:unless, 18, 145, 4, 150, 7]": {"[:else, 19, 145, 4, 150, 7]": 0, "[:then, 20, 146, 6, 149, 7]": 0}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/integration_setting.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, null, null, null, 1, null, null, 1, 1, 1, null, null, 1, null, null, 1, null, null, 1, 0, 0, 0, 0, null, null, null, 1, 1, null, null, 1, 1, null, 0, null, 1, null, null, null, 1, 2, null, null, 1, 2, null, null, 1, 0, null, null, 1, 0, null, null, 1, 1, null, null, null, 1, 4, null, 3, 3, null, 3, 2, 2, null, 1, null, null, null, 1, 3, null, 2, 2, null, 2, 1, 1, null, 1, null, null, null, 1, 6, null, 4, 4, null, null, 1, 9, null, null, 1, 1, null, null, null, 1, 2, null, null, 1, 0, null, null, null, 1, null, 2, null, 1, null, null, null, null, 1, 0, 0, 0, null, 0, null, null, 1, 1, null, null, 1, 2, null, null, null, 1, 0, 0, null, null, 1, 0, null, null, null, null, null, 1, null, 1, 35, null, 22, 0, 0, null, null, 22, 35, 0, null, null, null, null, 1, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, null, null, null, null, null, null, null, 1, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, null, null, null, null, null, null, null], "branches": {"[:unless, 0, 54, 2, 59, 5]": {"[:else, 1, 54, 2, 59, 5]": 1, "[:then, 2, 55, 4, 58, 55]": 0}, "[:case, 3, 67, 4, 72, 7]": {"[:when, 4, 69, 6, 69, 31]": 0, "[:else, 5, 71, 6, 71, 39]": 1}, "[:\"&.\", 6, 92, 19, 92, 39]": {"[:then, 7, 92, 19, 92, 39]": 1, "[:else, 8, 92, 19, 92, 39]": 0}, "[:if, 9, 97, 4, 97, 37]": {"[:then, 10, 97, 4, 97, 16]": 1, "[:else, 11, 97, 4, 97, 37]": 3}, "[:unless, 12, 102, 4, 107, 7]": {"[:else, 13, 106, 6, 106, 11]": 1, "[:then, 14, 103, 6, 104, 10]": 2}, "[:if, 15, 111, 4, 111, 37]": {"[:then, 16, 111, 4, 111, 16]": 1, "[:else, 17, 111, 4, 111, 37]": 2}, "[:if, 18, 116, 4, 121, 7]": {"[:then, 19, 117, 6, 118, 10]": 1, "[:else, 20, 120, 6, 120, 11]": 1}, "[:if, 21, 125, 4, 125, 37]": {"[:then, 22, 125, 4, 125, 16]": 2, "[:else, 23, 125, 4, 125, 37]": 4}, "[:if, 24, 190, 4, 190, 38]": {"[:then, 25, 190, 4, 190, 10]": 13, "[:else, 26, 190, 4, 190, 38]": 22}, "[:unless, 27, 192, 4, 195, 7]": {"[:else, 28, 192, 4, 195, 7]": 22, "[:then, 29, 193, 6, 194, 12]": 0}, "[:unless, 30, 198, 6, 200, 9]": {"[:else, 31, 198, 6, 200, 9]": 35, "[:then, 32, 199, 8, 199, 92]": 0}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/application_record.rb": {"lines": [1, 1, null], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/helpers/application_helper.rb": {"lines": [1, 1, 0, null, 0, 0, null, 0, null, 0, null, null], "branches": {"[:if, 0, 3, 4, 3, 30]": {"[:then, 1, 3, 4, 3, 13]": 0, "[:else, 2, 3, 4, 3, 30]": 0}, "[:if, 3, 8, 4, 8, 48]": {"[:then, 4, 8, 4, 8, 29]": 0, "[:else, 5, 8, 4, 8, 48]": 0}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/interactors/filter_blacklisted_products.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, 1, null, 1, null, 16, 2, 2, 2, 2, 2, null, null, null, 14, 12, 3, 3, 3, 3, 3, null, null, 9, null, 3, 3, null, null, 1, null, 1, 9, 9, null, 9, 9, null, null, 9, 9, null, 9, 21, null, 21, 11, 11, null, 10, null, null, null, null, 9, null, null, 8, 8, 8, null, 8, 8, null, null, 8, 7, null, null, null, 1, 9, null, 8, null, 0, null, 0, null, 1, null, null, null, 1, 9, null, null, 8, null, null, null, 1, 21, null, null, null, 21, null, null, null, null, null, null, null, null, null, null, null, null, null, 1, null, 9, 0, 8, 0, 8, 7, 1, 0, 1, 0, 1, 0, null, null, null, 1, 7, null, 7, null, null, null, null, null, null, null, null, null, null, null, 0, null, null], "branches": {"[:unless, 0, 22, 4, 28, 7]": {"[:else, 1, 22, 4, 28, 7]": 14, "[:then, 2, 23, 6, 27, 12]": 2}, "[:if, 3, 32, 4, 38, 7]": {"[:then, 4, 33, 6, 37, 12]": 3, "[:else, 5, 32, 4, 38, 7]": 9}, "[:if, 6, 62, 6, 67, 9]": {"[:then, 7, 63, 8, 64, 94]": 11, "[:else, 8, 66, 8, 66, 30]": 10}, "[:if, 9, 82, 4, 84, 7]": {"[:then, 10, 83, 6, 83, 73]": 7, "[:else, 11, 82, 4, 84, 7]": 1}, "[:case, 12, 88, 4, 97, 7]": {"[:when, 13, 90, 6, 90, 24]": 8, "[:when, 14, 92, 6, 92, 27]": 0, "[:when, 15, 94, 6, 94, 29]": 0, "[:else, 16, 96, 6, 96, 19]": 1}, "[:unless, 17, 101, 4, 101, 42]": {"[:else, 18, 101, 4, 101, 42]": 8, "[:then, 19, 101, 4, 101, 13]": 1}, "[:unless, 20, 109, 4, 109, 38]": {"[:else, 21, 109, 4, 109, 38]": 21, "[:then, 22, 109, 4, 109, 14]": 0}, "[:if, 23, 129, 4, 141, 7]": {"[:then, 24, 130, 6, 130, 46]": 0, "[:else, 25, 131, 4, 141, 7]": 8}, "[:if, 26, 131, 4, 141, 7]": {"[:then, 27, 132, 6, 132, 41]": 0, "[:else, 28, 133, 4, 141, 7]": 8}, "[:if, 29, 133, 4, 141, 7]": {"[:then, 30, 134, 6, 134, 41]": 7, "[:else, 31, 135, 4, 141, 7]": 1}, "[:if, 32, 135, 4, 141, 7]": {"[:then, 33, 136, 6, 136, 45]": 0, "[:else, 34, 137, 4, 141, 7]": 1}, "[:if, 35, 137, 4, 141, 7]": {"[:then, 36, 138, 6, 138, 40]": 0, "[:else, 37, 139, 4, 141, 7]": 1}, "[:if, 38, 139, 4, 141, 7]": {"[:then, 39, 140, 6, 140, 40]": 0, "[:else, 40, 139, 4, 141, 7]": 1}, "[:\"&.\", 41, 145, 18, 145, 57]": {"[:then, 42, 145, 18, 145, 57]": 7, "[:else, 43, 145, 18, 145, 57]": 0}, "[:unless, 44, 145, 4, 145, 57]": {"[:else, 45, 145, 4, 145, 57]": 7, "[:then, 46, 145, 4, 145, 10]": 0}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/activity_log.rb": {"lines": [1, 1, 1, 1, 1, null, 1, 1, 1, 1, 1, 1, 1, null, null, null, 1, null, null, null, null, null, null, null, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, null, 1, 7, null, null, null, null, null, null, null, null, null, null, 0, null, null, 1, 7, null, null, 1, 0, null, null, 1, 0, null, null, 1, 0, null, null, null, 1, 0, null, null, 0, null, null, null, null, null, null, null], "branches": {"[:\"&.\", 0, 52, 18, 52, 37]": {"[:then, 1, 52, 18, 52, 37]": 0, "[:else, 2, 52, 18, 52, 37]": 7}, "[:\"&.\", 3, 53, 18, 53, 36]": {"[:then, 4, 53, 18, 53, 36]": 0, "[:else, 5, 53, 18, 53, 36]": 7}}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/clients/fluid/callback_definitions.rb": {"lines": [null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, 0, 0, 0, null, null, null, null, 0, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/clients/fluid/callback_registrations.rb": {"lines": [null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, null, 0, 0, 0, null, null, null, null, null, 0, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, 0, null, null, null, null, null, null, null, null, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/clients/fluid/droplets.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/clients/fluid/webhooks.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/clients/fluid_client.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, null, 0, null, null, null, 0, null, 0, 0, 0, 0, null, 0, null, null, 0, null, 0, 0, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, null, null, null, 0, 0, 0, null, 0, 0, 0, null, null, null, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/admin/callbacks_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/admin/dashboard_controller.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/admin/droplets_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/admin/settings_controller.rb": {"lines": [0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/admin/users_controller.rb": {"lines": [0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/admin_controller.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/api/v1/avalara_controller.rb": {"lines": [null, null, 0, null, 0, 0, null, null, 0, null, 0, null, null, null, 0, null, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, 0, 0, 0, 0, 0, 0, null, null, null, 0, 0, null, null, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, null, 0, 0, 0, 0, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/api/v1/callbacks_controller.rb": {"lines": [null, null, 0, 0, null, null, null, null, null, null, null, 0, null, 0, null, null, 0, null, 0, 0, 0, 0, null, null, null, 0, 0, 0, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, null, 0, null, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, null, null, 0, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, null, null, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, null, null, null, null, null, 0, 0, null, 0, 0, null, 0, 0, 0, null, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, null, null, null, 0, null, 0, null, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, null, null, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/application_controller.rb": {"lines": [0, null, 0, null, null, 0, null, null, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, null, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/avalara_settings_controller.rb": {"lines": [null, null, 0, null, 0, null, 0, 0, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, null, null, null, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, 0, 0, null, null, 0, 0, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, null, null, null, 0, 0, null, null, 0, null, null, 0, 0, 0, 0, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, 0, 0, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, null, 0, 0, 0, 0, null, null, 0, null, 0, null, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/debug_controller.rb": {"lines": [0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/home_controller.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/controllers/webhooks_controller.rb": {"lines": [0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/interactors/calculate_tax_with_avalara.rb": {"lines": [null, null, null, null, 0, 0, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, null, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, null, 0, null, 0, null, 0, 0, null, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/jobs/droplet_reinstalled_job.rb": {"lines": [0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/lib/avalara_test_data.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/mailers/application_mailer.rb": {"lines": [0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/callback.rb": {"lines": [null, null, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/company.rb": {"lines": [0, 0, 0, null, 0, 0, null, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/company_setting.rb": {"lines": [null, null, null, null, 0, null, 0, null, 0, null, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/event.rb": {"lines": [0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/service_result.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, null, 0, 0, 0, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, null, null, null, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/setting.rb": {"lines": [null, null, 0, null, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/tax_calculation_log.rb": {"lines": [0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, null, null, null, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/user.rb": {"lines": [0, null, null, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/models/webhook.rb": {"lines": [0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/permissions/ability.rb": {"lines": [null, null, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/permissions/admin_permissions.rb": {"lines": [0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/permissions/permission_set.rb": {"lines": [0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/avalara_api/configuration.rb": {"lines": [null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/avalara_client.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, 0, null, null, null, 0, null, null, null, 0, null, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, null, 0, null, null, null, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/avalara_error_handler.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, null, 0, 0, 0, 0, null, null, null, null, null, 0, 0, 0, null, null, null, null, null, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, null, null, null, null, null, 0, 0, 0, 0, 0, null, 0, null, null, 0, 0, 0, 0, 0, 0, null, null, null, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, null, null, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, null, null, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, null, null, null, null, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, null, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, null, null, null, 0, 0, 0, 0, null, null, null, null, 0, null, null, 0, 0, null, null, null, 0, null, 0, 0, null, null, null, 0, null, 0, 0, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, null, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/avalara_payload_builder.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, null, null, null, 0, null, null, null, 0, null, null, null, 0, null, null, null, 0, null, null, null, 0, null, null, null, 0, null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, 0, null, null, 0, null, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, null, null, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/callback_registration_service.rb": {"lines": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null, 0, null, null, 0, null, null, null, null, null, null, null, 0, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, null, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, null, 0, 0, null, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/callback_sync_service.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/droplet_manager.rb": {"lines": [null, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/tax_calculation_cache.rb": {"lines": [null, null, 0, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, 0, null, null, null, null, null, null, null, null, null, null, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, null, null, null, null, 0, 0, 0, null, null, null, null, 0, 0, 0, null, null, 0, 0, 0, null, null, null, 0, 0, 0, null, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, null, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, null, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, null, 0, 0, null, 0, 0, 0, null, null, 0, 0, 0, null, null, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, 0, null, null, 0, 0, 0, null, null, 0, null, 0, 0, null, 0, 0, null, null, 0, null, 0, null, null, 0, 0, null, null, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, null, null, 0, 0, null, 0, 0, 0, 0, null, null, 0, 0, null, null, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/services/webhook_manager.rb": {"lines": [null, null, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, null, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/use_cases/droplet_use_case/base.rb": {"lines": [null, null, 0, 0, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/use_cases/droplet_use_case/create.rb": {"lines": [null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/app/use_cases/droplet_use_case/update.rb": {"lines": [null, null, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/lib/tasks/settings.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, null, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0], "branches": {}}, "/Users/<USER>/code/plum/fluid/droplets/workspace/fluid-droplet-avalara/lib/tasks/setup.rb": {"lines": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, null, 0, 0, 0, 0, 0, 0], "branches": {}}}, "timestamp": 1757461839}}