project_id = "fluid-417204"
region     = "us-west3"
zone       = "us-west3-b"

# variable module compute_instance and static_ip
vm_name      = "fluid-droplet-avalara-jobs"
machine_type = "e2-small"
# labels for the instance
environment            = "production"
purpose_compute_engine = "jobs"
project                = "fluid-droplet-avalara"

# service account email to compute engine
email_service_account = ""


# variable module container
#master key
container_rails_master_key        = "4031f0bbbf7893390d4b0fb52ac08ca1"
container_image                   = "us-west3-docker.pkg.dev/fluid-417204/fluid-droplet-avalara/fluid-droplet-avalara-rails/web:latest"
container_db_url_production       = ""
container_db_url_production_queue = ""
container_db_url_production_cache = ""
container_db_url_production_cable = ""

# variable module cloud_run fluid droplet exigo ordercalc rails
vpc_connector_cloud_run = "projects/fluid-417204/locations/us-west3/connectors/fluid-vpc-connect"
cloud_sql_instances_cloud_run = [
  "fluid-417204:us-west3:fluid-droplet-avalara"
]
environment_variables_cloud_run = {
  "CABLE_DATABASE_URL"  = "",
  "CACHE_DATABASE_URL"  = "",
  "DATABASE_URL"        = "",
  "QUEUE_DATABASE_URL"  = "",
  "RACK_ENV"            = "production",
  "RAILS_ENV"           = "production",
  "RAILS_LOG_TO_STDOUT" = "enabled",
  "RAILS_MASTER_KEY"    = "4031f0bbbf7893390d4b0fb52ac08ca1",
  "FLUID_API_TOKEN"     = "REPLACE_WITH_ACTUAL_TOKEN",
}
