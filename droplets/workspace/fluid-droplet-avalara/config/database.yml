default: &default
  adapter: postgresql
  encoding: unicode
  pool: <%= ENV.fetch("RAILS_MAX_THREADS") { 5 } %>

development:
  <<: *default
  database: droplet_template_development

test:
  <<: *default
  database: droplet_template_test

production:
  primary:
    <<: *default
    url: <%= ENV['DATABASE_URL'] %>
  cache:
    <<: *default
    migrations_paths: db/cable_migrate
    url: <%= ENV['CACHE_DATABASE_URL'] %>
  queue:
    <<: *default
    migrations_paths: db/queue_migrate
    url: <%= ENV['QUEUE_DATABASE_URL'] %>
  cable:
    <<: *default
    migrations_paths: db/cable_migrate
    url: <%= ENV['CABLE_DATABASE_URL'] %>
