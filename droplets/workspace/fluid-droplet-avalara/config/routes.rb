Rails.application.routes.draw do
  root "home#index"

  devise_for :users

  post "webhook", to: "webhooks#create", as: :webhook
  post "webhook", to: "webhooks#create"  # Alias for Fluid compatibility

  # Additional webhook routes for different Fluid configurations
  post "admin/settings/webhook", to: "webhooks#create"  # Alternative webhook path

  # Public routes for store-level configuration
  get "avalara/configure", to: "avalara_settings#show", as: :avalara_configure
  post "avalara/configure", to: "avalara_settings#create"
  patch "avalara/configure", to: "avalara_settings#update"
  post "avalara/test_connection", to: "avalara_settings#test_connection"
  get "avalara/logs", to: "avalara_settings#logs"
  get "avalara/activity_logs", to: "avalara_settings#activity_logs"
  delete "avalara/clear_logs", to: "avalara_settings#clear_logs"
  post "avalara/sync_status", to: "avalara_settings#sync_status"
  post "avalara/test_callback", to: "avalara_settings#test_callback"
  post "avalara/callbacks/register", to: "avalara_settings#register_callbacks"
  delete "avalara/callbacks/unregister", to: "avalara_settings#unregister_callbacks"
  get "avalara/callbacks/status", to: "avalara_settings#callback_status"
  get "avalara/debug_credentials", to: "avalara_settings#debug_credentials"
  post "avalara/copy_credentials", to: "avalara_settings#copy_credentials"

  # Product Blacklist routes
  get "avalara/product_blacklist", to: "avalara_settings#product_blacklist"
  post "avalara/product_blacklist/add", to: "avalara_settings#add_to_blacklist"
  delete "avalara/product_blacklist/remove", to: "avalara_settings#remove_from_blacklist"
  delete "avalara/product_blacklist/clear", to: "avalara_settings#clear_blacklist"
  get "avalara/fluid_products", to: "avalara_settings#fluid_products"

  namespace :admin do
    get "dashboard/index"
    resource :droplet, only: %i[ create update ]
    resources :settings, only: %i[ index edit update ]
    # TODO: Add avalara_settings admin routes when controller is implemented
    resources :users
    resources :callbacks, only: %i[ index show edit update ] do
      post :sync, on: :collection
    end
  end

  get "up" => "rails/health#show", as: :rails_health_check
  get "test" => "application#test_endpoint"
  get "fix_creds" => "application#fix_credentials"
  get "sql_fix" => "application#sql_fix"
  get "debug/request_info" => "debug#request_info"
  get "debug/companies" => "debug#companies"
  post "debug/fix_companies" => "debug#fix_companies"
  post "debug/associate_dri" => "debug#associate_dri"
  get "debug/associate/:dri/:company_id" => "debug#associate_dri_get"
  get "debug/uninstall/:company_id" => "debug#uninstall_company"
  get "debug/create_company/:dri" => "debug#create_company_for_dri"
  get "debug/associate/:dri/:company_id" => "debug#associate_dri_get"
  get "debug/fix_uuid/:company_id" => "debug#fix_company_uuid"
  get "debug/copy_creds" => "avalara_settings#copy_credentials"

  # API v1 Routes
  namespace :api do
    namespace :v1 do
      # Fluid Callback API
      resources :callbacks, only: [] do
        collection do
          post "update_cart_tax"
        end
      end

      # Avalara Direct API
      resources :avalara, only: [] do
        collection do
          post "calculate_tax"
          get "ping"
          get "example"
          post "test_auth"
          get "cache/stats", to: "avalara#cache_stats"
          delete "cache", to: "avalara#clear_cache"
          post "cache/warm", to: "avalara#warm_cache"
        end
      end
    end
  end

  # Legacy routes (for backward compatibility - consider deprecating)
  resources :tax_calculations, only: [:create] do
    collection do
      get :ping
      get :example
      post :test_auth
      get "cache/stats" => :cache_stats
      delete "cache" => :clear_cache
      post "cache/warm" => :warm_cache
    end
  end
end
