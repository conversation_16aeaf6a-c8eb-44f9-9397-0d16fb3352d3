# frozen_string_literal: true

require 'rails_helper'

RSpec.describe IntegrationSetting, type: :model do
  describe 'basic functionality' do
    let(:setting) {
 IntegrationSetting.new(company_id: 123, account_id: 'test', license_key: 'key', company_code: 'code') }

    it 'validates presence of company_id' do
      setting.company_id = nil
      expect(setting).not_to be_valid
      expect(setting.errors[:company_id]).to include("can't be blank")
    end
  end

  describe '.for_company' do
    it 'finds setting by company_id' do
      setting = IntegrationSetting.create!(company_id: 123, environment: 0, enabled: false)
      expect(IntegrationSetting.for_company(123)).to eq(setting)
    end

    it 'returns nil for non-existent company_id' do
      expect(IntegrationSetting.for_company(999)).to be_nil
    end
  end

  describe 'thread-local storage helpers' do
    let(:setting) { IntegrationSetting.create!(company_id: 456, environment: 0, enabled: false) }

    describe '.current_integration_setting' do
      it 'returns nil when no context is set' do
        expect(IntegrationSetting.current_integration_setting).to be_nil
      end

      it 'returns setting from thread context' do
        Thread.current[:current_integration_setting] = setting
        expect(IntegrationSetting.current_integration_setting).to eq(setting)
        Thread.current[:current_integration_setting] = nil
      end
    end

    describe '.set_current' do
      it 'sets the current integration setting in thread' do
        IntegrationSetting.set_current(setting)
        expect(Thread.current[:current_integration_setting]).to eq(setting)
        IntegrationSetting.clear_current
      end
    end

    describe '.clear_current' do
      it 'clears the current integration setting from thread' do
        Thread.current[:current_integration_setting] = setting
        IntegrationSetting.clear_current
        expect(Thread.current[:current_integration_setting]).to be_nil
      end
    end
  end

  describe '#credentials_valid?' do
    it 'returns true when all credentials are present' do
      setting = IntegrationSetting.new(
        company_id: 123,
        account_id: '123',
        license_key: 'key',
        company_code: 'code'
      )
      expect(setting.credentials_valid?).to be true
    end

    it 'returns false when account_id is missing' do
      setting = IntegrationSetting.new(
        company_id: 123,
        account_id: nil,
        license_key: 'key',
        company_code: 'code'
      )
      expect(setting.credentials_valid?).to be false
    end
  end

  describe '#base_url' do
    it 'returns sandbox URL by default' do
      setting = IntegrationSetting.new(company_id: 123)
      expect(setting.base_url).to eq('https://sandbox-rest.avatax.com')
    end
  end

  describe '#summary' do
    it 'returns a summary of the configuration' do
      setting = IntegrationSetting.new(
        company_id: 123,
        account_id: '123456',
        environment: :sandbox,
        enabled: true
      )
      summary = setting.summary
      expect(summary).to include('account_id: 123***')
      expect(summary).to include('environment: sandbox')
      expect(summary).to include('enabled: true')
    end
  end

  describe 'product blacklist functionality' do
    describe '#product_blacklist' do
      it 'returns empty array when no blacklist is configured' do
        setting = IntegrationSetting.new(company_id: 123)
        expect(setting.product_blacklist).to eq([])
      end

      it 'returns configured product IDs' do
        setting = IntegrationSetting.new(
          company_id: 123,
          product_blacklist: ["1001", "1002"]
        )
        expect(setting.product_blacklist).to eq(["1001", "1002"])
      end
    end

    describe '#add_to_blacklist' do
      let(:setting) { create(:integration_setting) }

      it 'adds product ID to blacklist' do
        result = setting.add_to_blacklist("1001")
        expect(result).to be true
        expect(setting.product_blacklist).to include("1001")
      end

      it 'does not add duplicate product IDs' do
        setting.add_to_blacklist("1001")
        result = setting.add_to_blacklist("1001")
        expect(result).to be false
        expect(setting.product_blacklist.count("1001")).to eq(1)
      end

      it 'returns false for blank product ID' do
        result = setting.add_to_blacklist("")
        expect(result).to be false
        expect(setting.product_blacklist).to be_empty
      end
    end

    describe '#remove_from_blacklist' do
      let(:setting) { create(:integration_setting, product_blacklist: ["1001", "1002"]) }

      it 'removes product ID from blacklist' do
        result = setting.remove_from_blacklist("1001")
        expect(result).to be true
        expect(setting.product_blacklist).not_to include("1001")
        expect(setting.product_blacklist).to include("1002")
      end

      it 'returns false when product ID not in blacklist' do
        result = setting.remove_from_blacklist("1003")
        expect(result).to be false
      end

      it 'returns false for blank product ID' do
        result = setting.remove_from_blacklist("")
        expect(result).to be false
      end
    end

    describe '#product_blacklisted?' do
      let(:setting) { create(:integration_setting, product_blacklist: ["1001", "1002"]) }

      it 'returns true for blacklisted product ID' do
        expect(setting.product_blacklisted?("1001")).to be true
        expect(setting.product_blacklisted?(1001)).to be true
      end

      it 'returns false for non-blacklisted product ID' do
        expect(setting.product_blacklisted?("1003")).to be false
        expect(setting.product_blacklisted?(1003)).to be false
      end

      it 'returns false for blank product ID' do
        expect(setting.product_blacklisted?("")).to be false
        expect(setting.product_blacklisted?(nil)).to be false
      end
    end

    describe '#blacklist_count' do
      it 'returns count of blacklisted products' do
        setting = create(:integration_setting, product_blacklist: ["1001", "1002", "1003"])
        expect(setting.blacklist_count).to eq(3)
      end

      it 'returns 0 for empty blacklist' do
        setting = create(:integration_setting, product_blacklist: [])
        expect(setting.blacklist_count).to eq(0)
      end
    end

    describe '#clear_blacklist!' do
      let(:setting) { create(:integration_setting, product_blacklist: ["1001", "1002"]) }

      it 'clears all blacklisted products' do
        setting.clear_blacklist!
        expect(setting.product_blacklist).to be_empty
      end
    end
  end
end
