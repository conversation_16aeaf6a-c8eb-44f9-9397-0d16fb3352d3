# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CallbacksController, type: :controller do
  let(:integration_setting) { create(:integration_setting, :enabled, :with_blacklist) }
  let(:company_id) { integration_setting.company_id }
  
  let(:cart_payload) do
    {
      "callback_name" => "update_cart_tax",
      "company_id" => company_id,
      "cart" => {
        "id" => 9,
        "currency_code" => "USD",
        "items" => [
          {
            "id" => 1001,
            "price" => "29.99",
            "quantity" => 2,
            "product" => {
              "sku" => "LEAN-GREENS-001",
              "title" => "Lean Greens - Packets",
            },
          },
          {
            "id" => 1002,
            "price" => "9.95",
            "quantity" => 1,
            "product" => {
              "sku" => "GIFT-CARD-50",
              "title" => "10 $50 Gift Cards",
            },
          },
        ],
        "ship_to" => {
          "address1" => "123 Main St",
          "city" => "Los Angeles",
          "country_code" => "US",
          "postal_code" => "90210",
          "state" => "CA",
        },
      }
    }
  end

  let(:successful_tax_result) do
    {
      total_tax: 5.25,
      subtotal: 59.98,
      total_amount: 65.23,
      tax_lines: [
        {
          line_number: "1",
          tax_amount: 5.25,
          taxable_amount: 59.98,
          description: "Lean Greens - Packets"
        }
      ]
    }
  end

  describe 'POST #tax' do
    context 'with blacklist filtering enabled' do
      before do
        allow_any_instance_of(FilterBlacklistedProducts).to receive(:call).and_return(
          double(success?: true, filtered_cart_data: filtered_cart_data)
        )
        
        allow_any_instance_of(CalculateTaxWithAvalara).to receive(:call).and_return(
          double(success?: true, tax_result: successful_tax_result)
        )
      end

      let(:filtered_cart_data) do
        cart_data = cart_payload["cart"].dup
        cart_data["items"] = cart_data["items"].reject { |item| item["product"]["sku"].include?("GIFT-CARD") }
        cart_data
      end

      it 'filters blacklisted products before tax calculation' do
        expect(FilterBlacklistedProducts).to receive(:call).with(
          cart_data: cart_payload["cart"],
          integration_setting: integration_setting
        ).and_return(double(success?: true, filtered_cart_data: filtered_cart_data))

        post :tax, params: cart_payload, as: :json
      end

      it 'passes filtered cart data to tax calculation' do
        expect(CalculateTaxWithAvalara).to receive(:call).with(
          hash_including(cart_data: filtered_cart_data)
        ).and_return(double(success?: true, tax_result: successful_tax_result))

        post :tax, params: cart_payload, as: :json
      end

      it 'returns successful tax calculation response' do
        post :tax, params: cart_payload, as: :json

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response["tax_total"]).to eq(5.25)
      end

      it 'logs blacklist filtering activity' do
        expect(Rails.logger).to receive(:info).with(/Processing tax callback with blacklist filtering/)

        post :tax, params: cart_payload, as: :json
      end
    end

    context 'without blacklist configured' do
      let(:integration_setting) { create(:integration_setting, :enabled) }

      before do
        allow_any_instance_of(FilterBlacklistedProducts).to receive(:call).and_return(
          double(success?: true, filtered_cart_data: cart_payload["cart"])
        )
        
        allow_any_instance_of(CalculateTaxWithAvalara).to receive(:call).and_return(
          double(success?: true, tax_result: successful_tax_result)
        )
      end

      it 'processes all products without filtering' do
        expect(FilterBlacklistedProducts).to receive(:call).with(
          cart_data: cart_payload["cart"],
          integration_setting: integration_setting
        ).and_return(double(success?: true, filtered_cart_data: cart_payload["cart"]))

        post :tax, params: cart_payload, as: :json
      end

      it 'passes original cart data to tax calculation' do
        expect(CalculateTaxWithAvalara).to receive(:call).with(
          hash_including(cart_data: cart_payload["cart"])
        ).and_return(double(success?: true, tax_result: successful_tax_result))

        post :tax, params: cart_payload, as: :json
      end
    end

    context 'when blacklist filtering fails' do
      before do
        allow_any_instance_of(FilterBlacklistedProducts).to receive(:call).and_return(
          double(success?: false, error: "Filtering failed")
        )
      end

      it 'returns error response' do
        post :tax, params: cart_payload, as: :json

        expect(response).to have_http_status(:unprocessable_entity)
        
        json_response = JSON.parse(response.body)
        expect(json_response["error"]).to include("Filtering failed")
      end

      it 'does not proceed to tax calculation' do
        expect(CalculateTaxWithAvalara).not_to receive(:call)

        post :tax, params: cart_payload, as: :json
      end
    end

    context 'with all products blacklisted' do
      let(:empty_cart_data) do
        cart_data = cart_payload["cart"].dup
        cart_data["items"] = []
        cart_data
      end

      before do
        allow_any_instance_of(FilterBlacklistedProducts).to receive(:call).and_return(
          double(success?: true, filtered_cart_data: empty_cart_data)
        )
        
        allow_any_instance_of(CalculateTaxWithAvalara).to receive(:call).and_return(
          double(success?: true, tax_result: { total_tax: 0.0, subtotal: 0.0, total_amount: 0.0 })
        )
      end

      it 'processes empty cart and returns zero tax' do
        post :tax, params: cart_payload, as: :json

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response["tax_total"]).to eq(0.0)
      end
    end

    context 'with missing integration setting' do
      let(:cart_payload_invalid) do
        payload = cart_payload.dup
        payload["company_id"] = 99999 # Non-existent company
        payload
      end

      it 'returns not found error' do
        post :tax, params: cart_payload_invalid, as: :json

        expect(response).to have_http_status(:not_found)
      end

      it 'does not attempt filtering or tax calculation' do
        expect(FilterBlacklistedProducts).not_to receive(:call)
        expect(CalculateTaxWithAvalara).not_to receive(:call)

        post :tax, params: cart_payload_invalid, as: :json
      end
    end

    context 'with disabled integration' do
      let(:integration_setting) { create(:integration_setting, :with_blacklist, enabled: false) }

      it 'returns service unavailable error' do
        post :tax, params: cart_payload, as: :json

        expect(response).to have_http_status(:service_unavailable)
      end

      it 'does not attempt filtering or tax calculation' do
        expect(FilterBlacklistedProducts).not_to receive(:call)
        expect(CalculateTaxWithAvalara).not_to receive(:call)

        post :tax, params: cart_payload, as: :json
      end
    end
  end

  describe 'POST #shipping' do
    let(:shipping_payload) do
      payload = cart_payload.dup
      payload["callback_name"] = "update_cart_shipping"
      payload
    end

    context 'with blacklist filtering' do
      before do
        allow_any_instance_of(FilterBlacklistedProducts).to receive(:call).and_return(
          double(success?: true, filtered_cart_data: cart_payload["cart"])
        )
      end

      it 'applies blacklist filtering to shipping calculations' do
        expect(FilterBlacklistedProducts).to receive(:call).with(
          cart_data: shipping_payload["cart"],
          integration_setting: integration_setting
        )

        post :shipping, params: shipping_payload, as: :json
      end

      it 'returns appropriate shipping response' do
        # Mock shipping calculation response
        allow(controller).to receive(:calculate_shipping).and_return({ shipping_total: 10.00 })

        post :shipping, params: shipping_payload, as: :json

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key("shipping_total")
      end
    end
  end
end
