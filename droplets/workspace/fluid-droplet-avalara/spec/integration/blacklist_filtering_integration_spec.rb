# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Blacklist Filtering Integration', type: :request do
  let(:integration_setting) do
    create(:integration_setting, :enabled, :with_comprehensive_blacklist)
  end
  
  let(:company_id) { integration_setting.company_id }

  let(:mixed_cart_payload) do
    {
      "callback_name" => "update_cart_tax",
      "company_id" => company_id,
      "cart" => {
        "id" => 9,
        "currency_code" => "USD",
        "items" => [
          {
            "id" => 1001,
            "price" => "41.00",
            "quantity" => 2,
            "product" => {
              "sku" => "LEAN-GREENS-001",
              "title" => "Lean Greens - Packets",
            },
          },
          {
            "id" => 1002,
            "price" => "9.95",
            "quantity" => 1,
            "product" => {
              "sku" => "GIFT-CARD-50",
              "title" => "10 $50 Gift Cards",
            },
          },
          {
            "id" => 1003,
            "price" => "25.00",
            "quantity" => 1,
            "product" => {
              "sku" => "REGULAR-PRODUCT",
              "title" => "Regular Product",
            },
          },
          {
            "id" => 1004,
            "price" => "15.00",
            "quantity" => 1,
            "product" => {
              "sku" => "VOUCHER-100",
              "title" => "Store Voucher",
            },
          },
        ],
        "ship_to" => {
          "address1" => "123 Main St",
          "city" => "Los Angeles",
          "country_code" => "US",
          "postal_code" => "90210",
          "state" => "CA",
        },
      }
    }
  end

  let(:successful_avalara_response) do
    ServiceResult.success({
      "id" => 123456,
      "code" => "ORDER-123",
      "totalAmount" => 107.00, # Only taxable products: $82 + $25 = $107
      "totalTax" => 9.10,      # ~8.5% tax rate
      "lines" => [
        {
          "lineNumber" => "1",
          "tax" => 6.97,
          "taxableAmount" => 82.00,
          "description" => "Lean Greens - Packets",
        },
        {
          "lineNumber" => "2", 
          "tax" => 2.13,
          "taxableAmount" => 25.00,
          "description" => "Regular Product",
        },
      ],
      "summary" => [
        {
          "country" => "US",
          "region" => "CA",
          "taxType" => "Sales",
          "tax" => 9.10,
        },
      ],
    })
  end

  describe 'End-to-End Tax Calculation with Blacklist' do
    before do
      allow_any_instance_of(AvalaraClient).to receive(:calculate_tax)
        .and_return(successful_avalara_response)
    end

    context 'with mixed cart (taxable and blacklisted products)' do
      it 'filters blacklisted products and calculates tax only for taxable items' do
        post '/callbacks/tax', params: mixed_cart_payload, as: :json

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        
        # Should return tax only for non-blacklisted products
        expect(json_response["tax_total"]).to eq(9.10)
        
        # Verify the calculation is based on filtered subtotal
        # Original cart: $82 + $9.95 + $25 + $15 = $131.95
        # Filtered cart: $82 + $25 = $107.00 (Gift Card and Voucher filtered)
        expect(json_response).to have_key("tax_total")
      end

      it 'logs the filtering process' do
        expect(Rails.logger).to receive(:info).with(/FilterBlacklistedProducts: Filtered 2 products/)
        expect(Rails.logger).to receive(:debug).with(/Filtered product: GIFT-CARD-50/)
        expect(Rails.logger).to receive(:debug).with(/Filtered product: VOUCHER-100/)

        post '/callbacks/tax', params: mixed_cart_payload, as: :json
      end

      it 'passes correct filtered data to Avalara' do
        expect_any_instance_of(AvalaraClient).to receive(:calculate_tax) do |client, payload|
          # Verify that only non-blacklisted products are sent to Avalara
          lines = payload[:lines]
          expect(lines.length).to eq(2)
          
          skus = lines.map { |line| line[:itemCode] }
          expect(skus).to include("LEAN-GREENS-001", "REGULAR-PRODUCT")
          expect(skus).not_to include("GIFT-CARD-50", "VOUCHER-100")
          
          successful_avalara_response
        end

        post '/callbacks/tax', params: mixed_cart_payload, as: :json
      end
    end

    context 'with all products blacklisted' do
      let(:all_blacklisted_payload) do
        payload = mixed_cart_payload.dup
        payload["cart"]["items"] = [
          {
            "id" => 1002,
            "price" => "9.95",
            "quantity" => 1,
            "product" => {
              "sku" => "GIFT-CARD-50",
              "title" => "10 $50 Gift Cards",
            },
          },
          {
            "id" => 1004,
            "price" => "15.00",
            "quantity" => 1,
            "product" => {
              "sku" => "VOUCHER-100",
              "title" => "Store Voucher",
            },
          },
        ]
        payload
      end

      it 'returns zero tax when all products are blacklisted' do
        # Mock empty cart response from Avalara
        empty_response = ServiceResult.success({
          "id" => 123456,
          "totalAmount" => 0.0,
          "totalTax" => 0.0,
          "lines" => [],
          "summary" => [],
        })
        
        allow_any_instance_of(AvalaraClient).to receive(:calculate_tax)
          .and_return(empty_response)

        post '/callbacks/tax', params: all_blacklisted_payload, as: :json

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response["tax_total"]).to eq(0.0)
      end
    end

    context 'with no blacklisted products' do
      let(:no_blacklist_payload) do
        payload = mixed_cart_payload.dup
        payload["cart"]["items"] = [
          {
            "id" => 1001,
            "price" => "41.00",
            "quantity" => 2,
            "product" => {
              "sku" => "LEAN-GREENS-001",
              "title" => "Lean Greens - Packets",
            },
          },
          {
            "id" => 1003,
            "price" => "25.00",
            "quantity" => 1,
            "product" => {
              "sku" => "REGULAR-PRODUCT",
              "title" => "Regular Product",
            },
          },
        ]
        payload
      end

      it 'processes all products when none are blacklisted' do
        post '/callbacks/tax', params: no_blacklist_payload, as: :json

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response["tax_total"]).to eq(9.10)
      end
    end
  end

  describe 'Performance and Edge Cases' do
    context 'with large cart containing many blacklisted items' do
      let(:large_cart_payload) do
        payload = mixed_cart_payload.dup
        items = []
        
        # Add 50 regular products
        (1..50).each do |i|
          items << {
            "id" => 2000 + i,
            "price" => "10.00",
            "quantity" => 1,
            "product" => {
              "sku" => "REGULAR-#{i}",
              "title" => "Regular Product #{i}",
            },
          }
        end
        
        # Add 50 blacklisted products
        (1..50).each do |i|
          items << {
            "id" => 3000 + i,
            "price" => "5.00",
            "quantity" => 1,
            "product" => {
              "sku" => "GIFT-CARD-#{i}",
              "title" => "Gift Card #{i}",
            },
          }
        end
        
        payload["cart"]["items"] = items
        payload
      end

      it 'efficiently filters large number of products' do
        # Mock response for 50 regular products
        large_response = ServiceResult.success({
          "id" => 123456,
          "totalAmount" => 500.00,
          "totalTax" => 42.50,
          "lines" => [],
          "summary" => [],
        })
        
        allow_any_instance_of(AvalaraClient).to receive(:calculate_tax)
          .and_return(large_response)

        expect {
          post '/callbacks/tax', params: large_cart_payload, as: :json
        }.not_to raise_error

        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response["tax_total"]).to eq(42.50)
      end
    end

    context 'with malformed product data' do
      let(:malformed_payload) do
        payload = mixed_cart_payload.dup
        payload["cart"]["items"] << {
          "id" => 9999,
          "price" => "10.00",
          "quantity" => 1,
          "product" => nil # Malformed product data
        }
        payload
      end

      it 'handles malformed product data gracefully' do
        post '/callbacks/tax', params: malformed_payload, as: :json

        expect(response).to have_http_status(:ok)
        
        # Should still process valid products
        json_response = JSON.parse(response.body)
        expect(json_response).to have_key("tax_total")
      end
    end
  end
end
