# frozen_string_literal: true

require 'rails_helper'

RSpec.describe FilterBlacklistedProducts do
  let(:integration_setting) { create(:integration_setting, :with_blacklist) }
  
  let(:cart_data) do
    {
      "id" => 9,
      "currency_code" => "USD",
      "items" => [
        {
          "id" => 1001,
          "price" => "29.99",
          "quantity" => 2,
          "product" => {
            "id" => 1001,
            "sku" => "LEAN-GREENS-001",
            "title" => "Lean Greens - Packets",
          },
        },
        {
          "id" => 1002,
          "price" => "9.95",
          "quantity" => 1,
          "product" => {
            "id" => 1002,
            "sku" => "GIFT-CARD-50",
            "title" => "10 $50 Gift Cards",
          },
        },
        {
          "id" => 1003,
          "price" => "15.00",
          "quantity" => 1,
          "product" => {
            "id" => 1003,
            "sku" => "REGULAR-PRODUCT",
            "title" => "Regular Product",
          },
        },
      ],
      "ship_to" => {
        "address1" => "123 Main St",
        "city" => "Los Angeles",
        "country_code" => "US",
        "postal_code" => "90210",
        "state" => "CA",
      },
    }
  end

  describe '#call' do
    context 'with blacklist configured' do
      let(:context_params) do
        {
          cart_data: cart_data,
          integration_setting: integration_setting
        }
      end

      it 'succeeds and filters blacklisted products' do
        result = described_class.call(context_params)

        expect(result).to be_success
        expect(result.filtered_cart_data).to be_present
      end

      it 'removes products matching blacklist product IDs' do
        result = described_class.call(context_params)
        filtered_items = result.filtered_cart_data["items"]

        # Should keep Lean Greens and Regular Product (IDs 1001, 1003)
        # Should filter out Gift Card (ID 1002)
        expect(filtered_items.length).to eq(2)

        product_ids = filtered_items.map { |item| item["product"]["id"] }
        expect(product_ids).to include(1001, 1003)
        expect(product_ids).not_to include(1002)
      end

      it 'preserves original cart structure' do
        result = described_class.call(context_params)
        filtered_cart = result.filtered_cart_data

        expect(filtered_cart["id"]).to eq(cart_data["id"])
        expect(filtered_cart["currency_code"]).to eq(cart_data["currency_code"])
        expect(filtered_cart["ship_to"]).to eq(cart_data["ship_to"])
      end

      it 'preserves item structure for non-blacklisted products' do
        result = described_class.call(context_params)
        filtered_items = result.filtered_cart_data["items"]

        lean_greens_item = filtered_items.find { |item| item["product"]["id"] == 1001 }

        expect(lean_greens_item["id"]).to eq(1001)
        expect(lean_greens_item["price"]).to eq("29.99")
        expect(lean_greens_item["quantity"]).to eq(2)
        expect(lean_greens_item["product"]["title"]).to eq("Lean Greens - Packets")
      end

      it 'logs filtered products' do
        allow(Rails.logger).to receive(:info)
        expect(Rails.logger).to receive(:info).with(/FilterBlacklistedProducts: Original cart has 3 items/)
        expect(Rails.logger).to receive(:info).with(/FilterBlacklistedProducts: Removed 1 blacklisted products/)

        described_class.call(context_params)
      end
    end

    context 'without blacklist configured' do
      let(:integration_setting) { create(:integration_setting) }
      let(:context_params) do
        {
          cart_data: cart_data,
          integration_setting: integration_setting
        }
      end

      it 'returns original cart data unchanged' do
        result = described_class.call(context_params)

        expect(result).to be_success
        expect(result.filtered_cart_data).to eq(cart_data)
        expect(result.filtered_cart_data["items"].length).to eq(3)
      end

      it 'logs no filtering' do
        allow(Rails.logger).to receive(:debug)
        expect(Rails.logger).to receive(:debug).with("FilterBlacklistedProducts: No blacklist configured, skipping filter")

        described_class.call(context_params)
      end
    end

    context 'with empty blacklist' do
      let(:integration_setting) do
        create(:integration_setting, product_blacklist: [])
      end
      
      let(:context_params) do
        {
          cart_data: cart_data,
          integration_setting: integration_setting
        }
      end

      it 'returns original cart data unchanged' do
        result = described_class.call(context_params)

        expect(result).to be_success
        expect(result.filtered_cart_data).to eq(cart_data)
        expect(result.filtered_cart_data["items"].length).to eq(3)
      end
    end

    context 'with all products blacklisted' do
      let(:integration_setting) do
        create(:integration_setting, product_blacklist: ["1001", "1002", "1003"])
      end
      
      let(:context_params) do
        {
          cart_data: cart_data,
          integration_setting: integration_setting
        }
      end

      it 'returns cart with empty items array' do
        result = described_class.call(context_params)

        expect(result).to be_success
        expect(result.filtered_cart_data["items"]).to be_empty
      end

      it 'logs all products filtered' do
        allow(Rails.logger).to receive(:info)
        expect(Rails.logger).to receive(:info).with(/FilterBlacklistedProducts: Original cart has 3 items/)
        expect(Rails.logger).to receive(:info).with(/FilterBlacklistedProducts: Removed 3 blacklisted products/)

        described_class.call(context_params)
      end
    end

    context 'with missing cart data' do
      let(:context_params) do
        {
          cart_data: nil,
          integration_setting: integration_setting
        }
      end

      it 'fails with appropriate error' do
        result = described_class.call(context_params)

        expect(result).to be_failure
        expect(result.error).to include("Error filtering blacklisted products")
      end
    end

    context 'with missing integration setting' do
      let(:context_params) do
        {
          cart_data: cart_data,
          integration_setting: nil
        }
      end

      it 'succeeds but does not filter products' do
        result = described_class.call(context_params)

        expect(result).to be_success
        expect(result.filtered_cart_data).to eq(cart_data)
        expect(result.blacklisted_products).to be_empty
        expect(result.is_empty_after_filter).to be false
      end

      it 'logs no integration setting' do
        expect(Rails.logger).to receive(:debug).with("FilterBlacklistedProducts: No integration setting provided, skipping filter")

        described_class.call(context_params)
      end
    end

    context 'with malformed cart data' do
      let(:malformed_cart) { { "id" => 9 } } # Missing items
      let(:context_params) do
        {
          cart_data: malformed_cart,
          integration_setting: integration_setting
        }
      end

      it 'handles missing items gracefully' do
        result = described_class.call(context_params)

        expect(result).to be_success
        expect(result.filtered_cart_data["items"] || []).to be_empty
      end
    end

    context 'when unexpected error occurs' do
      let(:context_params) do
        {
          cart_data: cart_data,
          integration_setting: integration_setting
        }
      end

      before do
        allow(integration_setting).to receive(:product_blacklist).and_raise(StandardError.new("Unexpected error"))
      end

      it 'fails gracefully with error message' do
        result = described_class.call(context_params)

        expect(result).to be_failure
        expect(result.error).to include("Unexpected error")
      end

      it 'logs the error' do
        expect(Rails.logger).to receive(:error).with(/FilterBlacklistedProducts Error: Unexpected error/)

        described_class.call(context_params)
      end
    end
  end
end
