# frozen_string_literal: true

FactoryBot.define do
  factory :integration_setting do
    sequence(:company_id) { |n| 1000 + n }
    account_id { "**********" }
    license_key { "2533BDD11FCBA864" }
    company_code { "FluidCommerceIncPartnerAc" }
    environment { 0 } # 0 = sandbox, 1 = production
    enabled { false }
    configuration { { created_via: "factory" } }
    sequence(:droplet_installation_uuid) { |n| "droplet-uuid-#{n}" }
    sequence(:authentication_token) { |n| "auth-token-#{n}" }

    trait :enabled do
      enabled { true }
    end

    trait :production do
      environment { :production }
    end

    trait :with_valid_credentials do
      account_id { "**********" }
      license_key { "2533BDD11FCBA864" }
      company_code { "FluidCommerceIncPartnerAc" }
    end

    trait :with_invalid_credentials do
      account_id { nil }
      license_key { nil }
      company_code { nil }
    end

    trait :with_blacklist do
      product_blacklist { ["1002"] } # Gift card product ID
    end

    trait :with_multiple_blacklist do
      product_blacklist { ["1002", "1004"] } # Gift card and voucher product IDs
    end

    trait :with_comprehensive_blacklist do
      product_blacklist { ["1002", "1004", "1005", "1006"] } # Multiple blacklisted product IDs
    end
  end
end
