# frozen_string_literal: true

module Admin
  # Controller for managing dynamic bundles in the admin interface
  # Provides CRUD operations for bundle management
  class BundlesController < ApplicationController
    layout 'bundle_admin'

    before_action :authenticate_admin! # Assuming admin authentication exists
    before_action :ensure_company_context
    before_action :set_bundle, only: [:show, :edit, :update, :destroy, :toggle_status]

    # GET /admin/bundles
    # Lists all dynamic bundles with pagination
    def index
      @page = params[:page]&.to_i || 1
      @per_page = params[:per_page]&.to_i || 20

      Rails.logger.info("Loading bundles from MOCK DATA - page: #{@page}, per_page: #{@per_page}")

      # MOCK DATA - Empty state to show no bundles
      @bundles = []

      @total_count = @bundles.length
      @pagination = {
        current_page: @page,
        per_page: @per_page,
        total_pages: 1,
        total_count: @total_count
      }

      Rails.logger.info("Successfully loaded #{@bundles.size} bundles from MOCK DATA")

      if @bundles.empty?
        flash.now[:info] = "No bundles found. Create your first dynamic bundle to get started!"
      end
    end

    # GET /admin/bundles/:id
    # Shows details of a specific bundle
    def show
      Rails.logger.info("Loading bundle #{params[:id]} from Fluid API")

      result = Fluid::BundlesService.call(
        action: :find,
        bundle_id: params[:id],
        company: @company
      )

      if result.success?
        @bundle_data = result.data[:bundle]
        Rails.logger.info("Successfully loaded bundle #{params[:id]} from Fluid API")
      else
        Rails.logger.error("Failed to load bundle #{params[:id]}: #{result.error}")
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundles_path
      end
    end

    # GET /admin/bundles/new
    # Redirects to the Bundle Creation Wizard
    def new
      # Start the wizard instead of showing a separate form
      redirect_to start_bundle_wizard_admin_bundles_path
    end

    # POST /admin/bundles
    # DEPRECATED: Now handled by the Bundle Wizard
    # Creates a new bundle shell and redirects to builder
    def create
      Rails.logger.info("Creating bundle with params: #{bundle_params.inspect}")

      # Set company context for validation
      Thread.current[:current_company] = @company

      @bundle = Bundle.new(bundle_params)

      # Auto-generate SKU if not provided
      if @bundle.sku.blank? && @bundle.name.present?
        @bundle.sku = Bundle.generate_sku_from_name(@bundle.name)
      end

      if @bundle.valid?
        # Create bundle shell via Fluid API
        result = Fluid::BundlesService.call(
          action: :create_shell,
          name: @bundle.name,
          sku: @bundle.sku,
          description: @bundle.description,
          company: @company
        )

        if result.success?
          bundle_data = result.data[:bundle]

          # Store bundle data in session for builder
          session[:bundle_draft] = bundle_data

          flash[:success] = "Bundle '#{@bundle.name}' created successfully! Now let's configure your categories."

          # Clear any existing session and redirect to wizard
          session[:bundle_draft] = nil
          redirect_to new_bundle_builder_admin_bundles_path
          Rails.logger.info("Bundle creation form completed, redirecting to wizard")
        else
          Rails.logger.error("Failed to create bundle via API: #{result.error}")
          flash.now[:error] = "Failed to create bundle: #{result.error}"
          render :new, status: :unprocessable_entity
        end
      else
        # Return to form with errors
        flash.now[:error] = "Please fix the errors below."
        render :new, status: :unprocessable_entity
      end
    end

    # GET /admin/bundles/:id/edit
    # Shows form for editing an existing bundle
    def edit
      Rails.logger.info("Loading bundle #{params[:id]} for editing from Fluid API")

      result = Fluid::BundlesService.call(
        action: :find,
        bundle_id: params[:id],
        company: @company
      )

      if result.success?
        bundle_data = result.data[:bundle]
        @bundle = Bundle.new(
          id: bundle_data['id'],
          name: bundle_data['name'],
          sku: bundle_data['sku'],
          description: bundle_data['description'],
          status: bundle_data['status']
        )
        Rails.logger.info("Successfully loaded bundle #{params[:id]} for editing")
      else
        Rails.logger.error("Failed to load bundle #{params[:id]} for editing: #{result.error}")
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundles_path
      end
    end

    # PATCH/PUT /admin/bundles/:id
    # Updates an existing bundle
    def update
      Rails.logger.info("Updating bundle #{params[:id]} via Fluid API with params: #{bundle_params.inspect}")

      # Set company context for validation
      Thread.current[:current_company] = @company

      # Create Bundle object for validation
      @bundle = Bundle.new(bundle_params.merge(id: params[:id]))

      if @bundle.valid?
        result = Fluid::BundlesService.call(
          action: :update,
          bundle_id: params[:id],
          name: @bundle.name,
          description: @bundle.description,
          company: @company
        )

        if result.success?
          flash[:success] = "Bundle '#{@bundle.name}' updated successfully!"
          redirect_to admin_bundle_path(params[:id])
          Rails.logger.info("Successfully updated bundle #{params[:id]} via API")
        else
          Rails.logger.error("Failed to update bundle #{params[:id]} via API: #{result.error}")
          flash.now[:error] = "Failed to update bundle: #{result.error}"
          render :edit, status: :unprocessable_entity
        end
      else
        flash.now[:error] = "Please fix the errors below."
        render :edit, status: :unprocessable_entity
      end
    end

    # DELETE /admin/bundles/:id
    # Deletes a bundle (if supported by API)
    def destroy
      Rails.logger.info("Deleting bundle #{params[:id]} via Fluid API")

      # Note: Fluid API may not support bundle deletion
      # For now, we'll show a not implemented message
      flash[:warning] = "Bundle deletion is not currently supported by the Fluid API."
      redirect_to admin_bundles_path

      Rails.logger.info("Bundle deletion requested but not implemented in API")
    rescue => e
      Rails.logger.error("Error in bundle deletion: #{e.message}")
      flash[:error] = "Failed to delete bundle: #{e.message}"
      redirect_to admin_bundles_path
    end

    # PATCH /admin/bundles/:id/toggle_status
    # Toggles bundle status between active/inactive
    def toggle_status
      Rails.logger.info("Toggling status for bundle #{params[:id]}")

      # First get current bundle to determine new status
      result = Fluid::BundlesService.call(
        action: :find,
        bundle_id: params[:id],
        company: @company
      )

      if result.success?
        current_status = result.data[:bundle]['status']
        new_status = current_status == 'active' ? 'inactive' : 'active'

        # Update bundle with new status
        update_result = Fluid::BundlesService.call(
          action: :update,
          bundle_id: params[:id],
          metadata: { status: new_status },
          company: @company
        )

        if update_result.success?
          flash[:success] = "Bundle status updated to #{new_status}!"
        else
          flash[:error] = "Failed to update bundle status: #{update_result.error}"
        end
      else
        flash[:error] = "Bundle not found: #{result.error}"
      end

      redirect_back(fallback_location: admin_bundles_path)
    rescue => e
      Rails.logger.error("Error toggling bundle status: #{e.message}")
      flash[:error] = "Failed to update bundle status: #{e.message}"
      redirect_back(fallback_location: admin_bundles_path)
    end

    # Mock endpoint for product search (will be replaced with real API)
    def search_products
      search_term = params[:search]&.downcase || ''
      page = params[:page]&.to_i || 1
      per_page = params[:per_page]&.to_i || 50

      # Mock product data with variants
      mock_products = [
        {
          id: 'prod1',
          name: 'Whey Protein',
          sku: 'WHEY-001',
          price: 49.99,
          variants: [
            { id: 'var1', title: 'Whey Protein - Vanilla', sku: 'WHEY-VAN-001', price: 49.99 },
            { id: 'var2', title: 'Whey Protein - Chocolate', sku: 'WHEY-CHO-001', price: 49.99 },
            { id: 'var3', title: 'Whey Protein - Strawberry', sku: 'WHEY-STR-001', price: 49.99 }
          ]
        },
        {
          id: 'prod2',
          name: 'Plant Protein',
          sku: 'PLANT-001',
          price: 54.99,
          variants: [
            { id: 'var4', title: 'Plant Protein - Berry', sku: 'PLANT-BER-001', price: 54.99 },
            { id: 'var5', title: 'Plant Protein - Vanilla', sku: 'PLANT-VAN-001', price: 54.99 }
          ]
        },
        {
          id: 'prod3',
          name: 'Energy Boost',
          sku: 'ENERGY-001',
          price: 34.99,
          variants: [
            { id: 'var6', title: 'Energy Boost - Citrus', sku: 'ENRG-CIT-001', price: 34.99 },
            { id: 'var7', title: 'Energy Boost - Berry Blast', sku: 'ENRG-BER-001', price: 34.99 }
          ]
        },
        {
          id: 'prod4',
          name: 'Focus Formula',
          sku: 'FOCUS-001',
          price: 39.99,
          variants: [
            { id: 'var8', title: 'Focus Formula - Unflavored', sku: 'FOCUS-UNF-001', price: 39.99 }
          ]
        },
        {
          id: 'prod5',
          name: 'BCAA Complex',
          sku: 'BCAA-001',
          price: 29.99,
          variants: [
            { id: 'var9', title: 'BCAA Complex - Lemon Lime', sku: 'BCAA-LEM-001', price: 29.99 },
            { id: 'var10', title: 'BCAA Complex - Orange', sku: 'BCAA-ORA-001', price: 29.99 }
          ]
        },
        {
          id: 'prod6',
          name: 'Recovery Plus',
          sku: 'RECOVERY-001',
          price: 44.99,
          variants: [
            { id: 'var11', title: 'Recovery Plus - Mixed Berry', sku: 'RECV-MIX-001', price: 44.99 }
          ]
        },
        {
          id: 'prod7',
          name: 'Daily Multi',
          sku: 'MULTI-001',
          price: 24.99,
          variants: [
            { id: 'var12', title: 'Daily Multi - Standard', sku: 'MULTI-STD-001', price: 24.99 },
            { id: 'var13', title: 'Daily Multi - Women\'s Formula', sku: 'MULTI-WOM-001', price: 27.99 },
            { id: 'var14', title: 'Daily Multi - Men\'s Formula', sku: 'MULTI-MEN-001', price: 27.99 }
          ]
        },
        {
          id: 'prod8',
          name: 'Premium Shaker Bottle',
          sku: 'SHAKE-001',
          price: 12.99,
          variants: [
            { id: 'var15', title: 'Premium Shaker Bottle', sku: 'SHAKE-001', price: 12.99 }
          ]
        },
        {
          id: 'prod9',
          name: 'Transformation Welcome Guide',
          sku: 'GUIDE-001',
          price: 0.00,
          variants: [
            { id: 'var16', title: 'Transformation Welcome Guide', sku: 'GUIDE-001', price: 0.00 }
          ]
        }
      ]

      # Flatten products to include variants as individual searchable items
      searchable_items = []
      mock_products.each do |product|
        product[:variants].each do |variant|
          searchable_items << {
            id: variant[:id],
            product_id: product[:id],
            name: variant[:title],
            sku: variant[:sku],
            price: variant[:price],
            parent_product: product[:name]
          }
        end
      end

      # Filter by search term
      if search_term.present?
        searchable_items = searchable_items.select do |item|
          item[:name].downcase.include?(search_term) ||
          item[:sku].downcase.include?(search_term) ||
          item[:parent_product].downcase.include?(search_term)
        end
      end

      # Pagination
      total = searchable_items.length
      start_index = (page - 1) * per_page
      end_index = start_index + per_page - 1
      paginated_items = searchable_items[start_index..end_index] || []

      render json: {
        success: true,
        products: paginated_items,
        pagination: {
          page: page,
          per_page: per_page,
          total: total,
          total_pages: (total.to_f / per_page).ceil
        }
      }
    end

    private

    # Set bundle for actions that need it
    def set_bundle
      @bundle_id = params[:id]
    end

    # Strong parameters for bundle creation/update
    def bundle_params
      params.require(:bundle).permit(:name, :sku, :description, :status)
    end

    # Ensure company context is available
    def ensure_company_context
      unless @company
        # HARDCODE FOR DEVELOPMENT - Remove when testing with real stores
        if Rails.env.development?
          @company = OpenStruct.new(
            fluid_company_id: *********,
            name: "Development Company",
            droplet_installation_uuid: "dev-dri-123",
            authentication_token: "your_real_fluid_token_here"
          )
          Rails.logger.info("HARDCODED: Using development company (ID: #{@company.fluid_company_id})")
          return
        end

        handle_missing_company_context
      end
    end

    # Admin authentication for bundle management
    # In droplets, admin access is typically controlled by company context
    # rather than individual user authentication
    def authenticate_admin!
      # For droplets, we authenticate via company context and API tokens
      # The company context is established through the droplet installation

      if Rails.env.development?
        Rails.logger.info("DEVELOPMENT: Admin access granted for development")
        return true
      end

      # In production, ensure we have valid company context
      # This means the request came through proper droplet installation
      unless @company&.authentication_token.present?
        Rails.logger.warn("Admin access denied: No valid company context")
        redirect_to root_path, alert: "Access denied: Invalid company context"
        return false
      end

      Rails.logger.info("Admin access granted for company: #{@company.name}")
      true
    end


  end
end
