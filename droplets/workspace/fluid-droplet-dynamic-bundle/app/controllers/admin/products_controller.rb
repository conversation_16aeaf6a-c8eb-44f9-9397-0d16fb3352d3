# frozen_string_literal: true

module Admin
  # Controller for managing product assignment to bundle categories
  # Handles product search, assignment, and ordering within categories
  class ProductsController < ApplicationController
    layout 'bundle_admin'
    
    before_action :authenticate_admin!
    before_action :set_bundle_and_category, except: [:search]
    before_action :set_product, only: [:assign, :unassign, :set_default, :move_up, :move_down]

    # GET /admin/products/search
    # API endpoint for product search in bundle wizard
    def search
      search_term = params[:q] || params[:search] || ''
      page = params[:page] || 1
      per_page = params[:per_page] || 50

      Rails.logger.info("PRODUCTS API: Search called with term: '#{search_term}', page: #{page}")

      # Check if settings exist and show credentials
      begin
        setting = Setting.fluid_api
        Rails.logger.info("PRODUCTS API: fluid_api setting found:")
        Rails.logger.info("PRODUCTS API: - base_url: #{setting.values['base_url']}")
        Rails.logger.info("PRODUCTS API: - api_key: #{setting.values['api_key'] ? setting.values['api_key'][0..10] + '...' : 'nil'}")
      rescue => setting_error
        Rails.logger.error("PRODUCTS API: fluid_api setting not found: #{setting_error.message}")
        Rails.logger.info("PRODUCTS API: Available settings: #{Setting.pluck(:name).inspect}")
      end

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        Rails.logger.warn("PRODUCTS API: FLUID_API_TOKEN not configured, using mock data")
        render json: generate_mock_products_response(search_term, page, per_page)
        return
      end

      # Try Fluid API first, fallback to mock data if API fails
      begin
        Rails.logger.info("PRODUCTS API: FLUID_API_TOKEN configured, fetching real products...")

        result = if search_term.present?
          Fluid::ProductsService.call(
            action: :search,
            query: search_term,
            page: page.to_i,
            per_page: per_page.to_i
          )
        else
          Fluid::ProductsService.call(
            action: :list,
            page: page.to_i,
            per_page: per_page.to_i
          )
        end

        if result.success?
          Rails.logger.info("PRODUCTS API: Successfully retrieved #{result.data[:products]&.length || 0} products from Fluid API")

          render json: {
            success: true,
            products: result.data[:products] || [],
            pagination: result.data[:pagination] || {}
          }
          return
        else
          Rails.logger.warn("PRODUCTS API: Fluid API failed - #{result.error}. Falling back to mock data.")
        end
      rescue => e
        Rails.logger.warn("PRODUCTS API: Fluid API exception - #{e.message}. Falling back to mock data.")
      end

      # Fallback to mock data
      Rails.logger.info("PRODUCTS API: Using mock data")
      products = generate_mock_products(search_term)

      # Paginate results
      total = products.length
      start_index = (page.to_i - 1) * per_page.to_i
      end_index = start_index + per_page.to_i - 1
      paginated_products = products[start_index..end_index] || []

      pagination = {
        page: page.to_i,
        per_page: per_page.to_i,
        total: total,
        total_pages: (total.to_f / per_page.to_i).ceil
      }

      render json: {
        success: true,
        products: paginated_products,
        pagination: pagination
      }
    rescue => e
      Rails.logger.error("PRODUCTS API: Exception - #{e.message}")
      render json: {
        success: false,
        error: e.message,
        products: [],
        pagination: { page: 1, per_page: per_page, total: 0, total_pages: 0 }
      }, status: 500
    end

    # GET /admin/bundles/:bundle_id/categories/:category_id/products
    # Shows product assignment interface with search
    def index
      @page = params[:page]&.to_i || 1
      @per_page = params[:per_page]&.to_i || 20
      @search_query = params[:search] || ""
      
      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundle_categories_path(@bundle_id)
        return
      end

      # Search available products using Fluid API
      result = Fluid::ProductsService.call(
        action: :search,
        query: @search_query,
        page: @page,
        per_page: @per_page
      )

      if result.success?
        @available_products = result.data[:products] || []
        @pagination = result.data[:pagination] || {}
      else
        @available_products = []
        @pagination = {}
        flash.now[:error] = "Failed to search products: #{result.error}"
      end

      @assigned_products = @category['products'] || []
    end

    # POST /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/assign
    # Assigns a product to the category
    def assign
      variant_id = params[:variant_id]
      product_id = params[:product_id] || params[:id]

      # Get product details from Fluid API
      product_result = Fluid::ProductsService.call(action: :find, product_id: product_id)
      unless product_result.success?
        flash[:error] = "Failed to load product details: #{product_result.error}"
        redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
        return
      end

      product_data = product_result.data[:product]
      variant = product_data['variants']&.find { |v| v['id'] == variant_id }

      unless variant
        flash[:error] = "Product variant not found"
        redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
        return
      end

      # Prepare product data for assignment
      assignment_data = {
        'productId' => product_id,
        'variantId' => variant_id,
        'variantTitle' => variant['title'],
        'variantSku' => variant['sku'],
        'price' => variant['price'],
        'isDefault' => false
      }

      # Use BundleMetadataService to assign product
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :assign_product,
        category_id: @category_id,
        product_data: assignment_data
      )

      if result.success?
        flash[:success] = "Product '#{variant['title']}' assigned to category successfully!"
      else
        flash[:error] = "Failed to assign product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    rescue => e
      flash[:error] = "Failed to assign product: #{e.message}"
      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # DELETE /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/unassign
    # Removes a product from the category
    def unassign
      product_id = params[:product_id] || params[:id]

      # Use BundleMetadataService to unassign product
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :unassign_product,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        product_title = result.data[:removed_product]['variantTitle'] rescue 'Product'
        flash[:success] = "#{product_title} removed from category successfully!"
      else
        flash[:error] = "Failed to remove product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    rescue => e
      flash[:error] = "Failed to remove product: #{e.message}"
      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/set_default
    # Sets a product as the default selection for the category
    def set_default
      product_id = params[:product_id] || params[:id]

      # Use BundleMetadataService to set default product
      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :set_default_product,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        flash[:success] = "Default product updated successfully!"
      else
        flash[:error] = "Failed to set default product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    rescue => e
      flash[:error] = "Failed to set default product: #{e.message}"
      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/move_up
    # Moves product up in display order
    def move_up
      product_id = params[:product_id] || params[:id]

      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_product_up,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        flash[:success] = "Product moved up successfully!"
      else
        flash[:error] = "Failed to move product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    # PATCH /admin/bundles/:bundle_id/categories/:category_id/products/:product_id/move_down
    # Moves product down in display order
    def move_down
      product_id = params[:product_id] || params[:id]

      result = BundleMetadataService.call(
        bundle_id: @bundle_id,
        action: :move_product_down,
        category_id: @category_id,
        product_id: product_id
      )

      if result.success?
        flash[:success] = "Product moved down successfully!"
      else
        flash[:error] = "Failed to move product: #{result.error}"
      end

      redirect_to admin_bundle_category_products_path(@bundle_id, @category_id)
    end

    private

    # Set bundle and category from params
    def set_bundle_and_category
      @bundle_id = params[:bundle_id]
      @category_id = params[:category_id]

      # Check if API token is configured
      if ENV['FLUID_API_TOKEN'].blank?
        flash[:error] = "Fluid API token not configured. Set FLUID_API_TOKEN environment variable."
        redirect_to admin_bundle_categories_path(@bundle_id)
        return
      end

      # Load bundle data from API
      result = Fluid::BundlesService.call(action: :find, bundle_id: @bundle_id)
      if result.success?
        @bundle = result.data[:bundle]
        categories = @bundle.dig('metadata', 'categories') || []
        @category = categories.find { |cat| cat['categoryId'] == @category_id }
      else
        flash[:error] = "Bundle not found: #{result.error}"
        redirect_to admin_bundle_categories_path(@bundle_id)
        return
      end

      unless @category
        flash[:error] = "Category not found"
        redirect_to admin_bundle_categories_path(@bundle_id)
      end
    end

    # Set product for actions that need it
    def set_product
      @product_id = params[:product_id] || params[:id]
    end

    # Placeholder for admin authentication
    def authenticate_admin!
      return true if Rails.env.development?
      redirect_to root_path unless current_user&.admin?
    end


    private

    # Mock available products for search
    def mock_available_products
      [
        {
          'id' => 'prod1',
          'name' => 'Premium Whey Protein',
          'sku' => 'WHEY-PREM-001',
          'description' => 'High-quality whey protein isolate',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var1',
              'title' => 'Whey Protein - Vanilla',
              'sku' => 'WHEY-VAN-001',
              'price' => 49.99,
              'inventory_quantity' => 100
            },
            {
              'id' => 'var2',
              'title' => 'Whey Protein - Chocolate',
              'sku' => 'WHEY-CHO-001',
              'price' => 49.99,
              'inventory_quantity' => 85
            },
            {
              'id' => 'var3',
              'title' => 'Whey Protein - Strawberry',
              'sku' => 'WHEY-STR-001',
              'price' => 49.99,
              'inventory_quantity' => 92
            }
          ]
        },
        {
          'id' => 'prod2',
          'name' => 'Plant-Based Protein',
          'sku' => 'PLANT-PROT-001',
          'description' => 'Organic plant-based protein blend',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var4',
              'title' => 'Plant Protein - Berry',
              'sku' => 'PLANT-BER-001',
              'price' => 54.99,
              'inventory_quantity' => 67
            },
            {
              'id' => 'var5',
              'title' => 'Plant Protein - Vanilla',
              'sku' => 'PLANT-VAN-001',
              'price' => 54.99,
              'inventory_quantity' => 43
            }
          ]
        },
        {
          'id' => 'prod3',
          'name' => 'Energy Pre-Workout',
          'sku' => 'ENERGY-PRE-001',
          'description' => 'High-energy pre-workout formula',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var6',
              'title' => 'Energy Boost - Citrus',
              'sku' => 'ENERGY-CIT-001',
              'price' => 34.99,
              'inventory_quantity' => 156
            },
            {
              'id' => 'var7',
              'title' => 'Energy Boost - Berry',
              'sku' => 'ENERGY-BER-001',
              'price' => 34.99,
              'inventory_quantity' => 134
            }
          ]
        },
        {
          'id' => 'prod4',
          'name' => 'BCAA Recovery',
          'sku' => 'BCAA-REC-001',
          'description' => 'Branched-chain amino acids for recovery',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var8',
              'title' => 'BCAA - Tropical',
              'sku' => 'BCAA-TROP-001',
              'price' => 39.99,
              'inventory_quantity' => 78
            }
          ]
        },
        {
          'id' => 'prod5',
          'name' => 'Creatine Monohydrate',
          'sku' => 'CREAT-MONO-001',
          'description' => 'Pure creatine monohydrate powder',
          'status' => 'active',
          'variants' => [
            {
              'id' => 'var9',
              'title' => 'Creatine - Unflavored',
              'sku' => 'CREAT-UNF-001',
              'price' => 24.99,
              'inventory_quantity' => 203
            }
          ]
        }
      ]
    end

    def generate_mock_products_response(search_term, page, per_page)
      Rails.logger.info("PRODUCTS API: Generating mock products response for search: '#{search_term}', page: #{page}")

      # Generate mock products based on search term
      mock_products = if search_term.present?
        generate_search_mock_products(search_term, per_page)
      else
        generate_default_mock_products(per_page)
      end

      {
        success: true,
        products: mock_products,
        pagination: {
          current_page: page,
          per_page: per_page,
          total_pages: 1,
          total_count: mock_products.size
        }
      }
    end

    def generate_search_mock_products(search_term, count)
      base_products = [
        { id: 'search-1', name: "#{search_term.titleize} Premium", price: '$29.99', sku: 'SEARCH-001' },
        { id: 'search-2', name: "#{search_term.titleize} Advanced", price: '$39.99', sku: 'SEARCH-002' },
        { id: 'search-3', name: "#{search_term.titleize} Pro", price: '$49.99', sku: 'SEARCH-003' }
      ]

      base_products.take(count)
    end

    def generate_default_mock_products(count)
      base_products = [
        { id: 'mock-1', name: 'Vitamin D3 5000 IU', price: '$24.99', sku: 'VIT-D3-001' },
        { id: 'mock-2', name: 'Omega-3 Fish Oil', price: '$32.99', sku: 'OMEGA-001' },
        { id: 'mock-3', name: 'Magnesium Glycinate', price: '$19.99', sku: 'MAG-001' },
        { id: 'mock-4', name: 'Whey Protein Vanilla', price: '$45.99', sku: 'WHEY-VAN-001' },
        { id: 'mock-5', name: 'Creatine Monohydrate', price: '$22.99', sku: 'CREAT-001' }
      ]

      base_products.take(count)
    end
  end
end
