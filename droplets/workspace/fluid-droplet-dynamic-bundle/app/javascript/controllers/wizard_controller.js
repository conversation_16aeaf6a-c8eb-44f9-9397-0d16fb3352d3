import { Controller } from "@hotwired/stimulus"

console.log("🧙‍♂️ WIZARD Controller file loading...")

export default class extends Controller {
  static targets = ["nextButton"]

  connect() {
    console.log("🧙‍♂️ WIZARD Controller connected")
  }

  nextStep(event) {
    console.log("🧙‍♂️ NEXT STEP clicked!")
    event.preventDefault()

    // Find the form and submit it with Turbo
    const form = this.element.querySelector('form')
    console.log("🧙‍♂️ Found form:", form)

    if (form) {
      console.log("🧙‍♂️ Submitting form with Turbo...")

      // Create a submit button and click it to trigger proper form submission
      const submitButton = document.createElement('button')
      submitButton.type = 'submit'
      submitButton.style.display = 'none'
      form.appendChild(submitButton)
      submitButton.click()
      form.removeChild(submitButton)
    } else {
      console.log("🧙‍♂️ No form found!")
    }
  }
}
