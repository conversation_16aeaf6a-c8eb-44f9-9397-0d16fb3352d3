import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = [
    "bundleName",
    "bundleSku", 
    "bundleDescription",
    "productCount",
    "productsList",
    "totalPrice",
    "completeButton"
  ]

  connect() {
    console.log("👀 Bundle Preview Controller connected!")

    // Load data from script tags instead of data attributes
    this.loadDataFromScripts()

    console.log("Bundle data:", this.bundleData)
    console.log("Products:", this.products)

    this.updatePreview()
  }

  loadDataFromScripts() {
    try {
      // Load bundle data
      const bundleDataScript = document.getElementById('bundle-data')
      if (bundleDataScript) {
        this.bundleData = JSON.parse(bundleDataScript.textContent)
      } else {
        this.bundleData = { name: 'Untitled Bundle', sku: 'No SKU', description: 'No description' }
      }

      // Load products data
      const productsDataScript = document.getElementById('products-data')
      if (productsDataScript) {
        this.products = JSON.parse(productsDataScript.textContent)
      } else {
        this.products = []
      }
    } catch (error) {
      console.error("Error loading data from scripts:", error)
      this.bundleData = { name: 'Untitled Bundle', sku: 'No SKU', description: 'No description' }
      this.products = []
    }
  }

  // Update all preview sections
  updatePreview() {
    this.updateBundleInfo()
    this.updateProductsList()
    this.updateStats()
    this.updateCompleteButton()
  }

  // Update bundle basic information
  updateBundleInfo() {
    const bundleData = this.bundleData || {}

    if (this.hasBundleNameTarget) {
      this.bundleNameTarget.textContent = bundleData.name || 'Untitled Bundle'
    }

    if (this.hasBundleSkuTarget) {
      this.bundleSkuTarget.textContent = bundleData.sku || 'No SKU'
    }

    if (this.hasBundleDescriptionTarget) {
      const description = bundleData.description || 'No description provided'
      this.bundleDescriptionTarget.textContent = description

      // Hide description section if empty
      const descriptionContainer = this.bundleDescriptionTarget.closest('.description-container')
      if (descriptionContainer) {
        descriptionContainer.style.display = bundleData.description ? 'block' : 'none'
      }
    }
  }

  // Update products list display
  updateProductsList() {
    if (!this.hasProductsListTarget) return

    const products = this.products || []

    if (products.length === 0) {
      this.showEmptyState()
      return
    }

    const productsHTML = products.map(product => this.renderProductCard(product)).join('')
    this.productsListTarget.innerHTML = productsHTML
  }

  // Render individual product card
  renderProductCard(product) {
    return `
      <div class="product-preview-card">
        <div class="product-image">
          ${product.image_url ? 
            `<img src="${product.image_url}" alt="${product.name}" />` :
            `<span class="product-emoji">${product.emoji || '📦'}</span>`
          }
        </div>
        <div class="product-info">
          <h6 class="product-name">${product.name || product.title}</h6>
          <div class="product-details">
            <span class="product-price">${product.price || '$0.00'}</span>
            ${product.sku ? `<span class="product-sku">SKU: ${product.sku}</span>` : ''}
          </div>
        </div>
      </div>
    `
  }

  // Show empty state when no products
  showEmptyState() {
    this.productsListTarget.innerHTML = `
      <div class="empty-state text-center py-5">
        <div class="empty-icon mb-3">📦</div>
        <h5 class="text-muted">No Products Selected</h5>
        <p class="text-muted mb-0">
          Your bundle doesn't have any products yet. 
          <a href="#" onclick="history.back()" class="text-primary">Go back to add products</a>
        </p>
      </div>
    `
  }

  // Update statistics
  updateStats() {
    const products = this.products || []
    const productCount = products.length

    if (this.hasProductCountTarget) {
      this.productCountTarget.textContent = productCount
    }

    if (this.hasTotalPriceTarget) {
      const totalPrice = this.calculateTotalPrice(products)
      this.totalPriceTarget.textContent = totalPrice
    }
  }

  // Calculate total price of all products
  calculateTotalPrice(products) {
    const total = products.reduce((sum, product) => {
      const price = parseFloat(product.price?.replace(/[^0-9.]/g, '') || 0)
      return sum + price
    }, 0)
    
    return `$${total.toFixed(2)}`
  }

  // Update complete button state
  updateCompleteButton() {
    if (!this.hasCompleteButtonTarget) return

    const products = this.products || []
    const hasProducts = products.length > 0
    const bundleData = this.bundleData || {}
    const hasBasicInfo = bundleData.name && bundleData.sku

    const canComplete = hasProducts && hasBasicInfo

    this.completeButtonTarget.disabled = !canComplete

    if (canComplete) {
      this.completeButtonTarget.classList.remove('btn-outline-success')
      this.completeButtonTarget.classList.add('btn-success')
    } else {
      this.completeButtonTarget.classList.remove('btn-success')
      this.completeButtonTarget.classList.add('btn-outline-success')
    }
  }

  // Handle complete bundle action
  completeBundle(event) {
    event.preventDefault()

    const products = this.products || []
    const bundleData = this.bundleData || {}

    if (products.length === 0) {
      alert('Please add at least one product to the bundle.')
      return
    }

    if (!bundleData.name || !bundleData.sku) {
      alert('Please ensure bundle name and SKU are provided.')
      return
    }

    // Show confirmation
    const productCount = products.length
    const message = `Create bundle "${bundleData.name}" with ${productCount} product${productCount !== 1 ? 's' : ''}?`

    if (confirm(message)) {
      console.log('🚀 Creating bundle with data:', { bundleData, products })

      // Submit the form or make API call
      const form = document.querySelector('#bundle-complete-form')
      if (form) {
        form.submit()
      } else {
        console.error('Complete form not found')
      }
    }
  }

  // Handle edit bundle info
  editInfo(event) {
    event.preventDefault()
    window.location.href = '/admin/bundles/wizard/step/info'
  }

  // Handle edit products
  editProducts(event) {
    event.preventDefault()
    window.location.href = '/admin/bundles/wizard/step/builder'
  }
}
