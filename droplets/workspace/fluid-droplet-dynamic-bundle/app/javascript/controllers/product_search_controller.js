import { Controller } from "@hotwired/stimulus"

// Product Search Controller - Hybrid search with initial load + API filtering
// Based on Avalara search functionality, adapted for Stimulus
export default class extends Controller {
  static targets = [
    "searchInput",
    "searchButton", 
    "productsContainer",
    "productsList",
    "productsLoading",
    "productsEmpty",
    "productsCount",
    "productsCountNumber"
  ]

  static values = {
    searchUrl: String,
    perPage: { type: Number, default: 50 },
    debounceDelay: { type: Number, default: 500 }
  }

  connect() {
    console.log("🔍 Product Search Controller connected!")

    // Initialize state
    this.isLoading = false
    this.currentProducts = []
    this.currentPage = 1
    this.totalPages = 1
    this.searchTimeout = null

    // Load initial products after a short delay to ensure DOM is ready
    setTimeout(() => {
      this.loadProducts('', 1)
    }, 100)
  }

  disconnect() {
    // Clear any pending timeouts
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }
  }

  // Search button click
  search() {
    const searchTerm = this.searchInputTarget.value.trim()
    this.loadProducts(searchTerm, 1)
  }

  // Search products on input (for real-time search)
  searchProducts() {
    // Clear any existing timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }

    // Debounce the search
    this.searchTimeout = setTimeout(() => {
      const searchTerm = this.searchInputTarget.value.trim()
      this.loadProducts(searchTerm, 1)
    }, this.debounceDelayValue)
  }

  // Real-time search as user types (with debounce)
  searchInput(event) {
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout)
    }

    const searchTerm = event.target.value.trim()

    // Immediate search for empty (show initial products)
    if (searchTerm === '') {
      this.loadProducts('', 1)
      return
    }

    // Debounced search for non-empty terms
    this.searchTimeout = setTimeout(() => {
      this.loadProducts(searchTerm, 1)
    }, this.debounceDelayValue)
  }

  // Handle Enter key in search input
  searchKeypress(event) {
    if (event.key === 'Enter') {
      event.preventDefault()
      this.search()
    }
  }

  // Main function to load products from API
  async loadProducts(search = '', page = 1) {
    console.log(`🔍 Loading products: search="${search}", page=${page}`)

    if (this.isLoading) {
      console.log('🔍 Already loading, skipping...')
      return // Prevent multiple simultaneous requests
    }

    this.isLoading = true
    this.showLoading()

    // Clear list only for first page
    if (page === 1) {
      this.productsListTarget.innerHTML = ''
    }
    this.hideEmpty()

    const params = new URLSearchParams({
      per_page: this.perPageValue,
      page: page
    })

    if (search.trim()) {
      params.append('search', search.trim())
    }

    const url = `${this.searchUrlValue}?${params}`
    console.log(`🔍 Fetching from: ${url}`)

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        }
      })

      console.log(`🔍 Response status: ${response.status}`)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      console.log('🔍 Response data:', data)

      this.isLoading = false
      this.hideLoading()

      if (data.success && data.products && data.products.length > 0) {
        console.log(`🔍 Found ${data.products.length} products`)
        this.currentProducts = data.products
        this.currentPage = data.pagination?.page || page
        this.totalPages = data.pagination?.total_pages || 1

        this.renderProducts(data.products)
        this.updateProductsCount(data.pagination?.total || data.products.length)

        // Dispatch custom event for successful load
        this.dispatch('productsLoaded', {
          detail: {
            products: data.products,
            pagination: data.pagination,
            search: search
          }
        })
      } else {
        console.log('🔍 No products found or API returned error')
        this.currentProducts = []
        this.showEmpty()
        this.updateProductsCount(0)

        if (search.trim()) {
          console.log(`No products found matching "${search}"`)
        } else {
          console.log('No products found')
        }
      }
    } catch (error) {
      this.isLoading = false
      this.hideLoading()
      console.error('🔍 Error loading products:', error)
      this.showEmpty()
      this.updateProductsCount(0)
    }
  }

  // Render products as draggable cards
  renderProducts(products) {
    const html = products.map(product => this.createProductCard(product)).join('')
    this.productsListTarget.innerHTML = html
  }

  // Create individual product card HTML (Bootstrap version with variants)
  createProductCard(product) {
    const hasVariants = product.variants && product.variants.length > 1
    const variantsCount = hasVariants ? product.variants.length : 0
    const chevronIcon = hasVariants ? '▶' : ''

    // Create variants HTML if they exist
    const variantsHtml = hasVariants ? `
      <div class="variants-container mt-2" style="display: none;" data-product-variants="${product.id}">
        <div class="border-top pt-2">
          <small class="text-muted d-block mb-1">Variants:</small>
          ${product.variants.map(variant => `
            <div class="d-flex justify-content-between align-items-center py-1 px-2 bg-light rounded mb-1" style="font-size: 0.75rem;">
              <span class="text-truncate me-2">${variant.title || variant.name || 'Variant'}</span>
              <span class="text-muted">${variant.sku || 'N/A'}</span>
            </div>
          `).join('')}
        </div>
      </div>
    ` : ''

    return `
      <div class="card mb-2 product-card"
           draggable="true"
           data-product-id="${product.id}"
           data-product-sku="${product.sku || ''}"
           data-product-name="${product.name || product.title || ''}"
           data-product-price="${product.price || 0}"
           data-action="dragstart->product-search#dragStart ${hasVariants ? 'click->product-search#toggleVariants' : ''}"
           style="cursor: move;">
        <div class="card-body p-2">
          <div class="d-flex align-items-center">
            <div class="me-2">
              <div class="bg-light rounded d-flex align-items-center justify-content-center"
                   style="width: 40px; height: 40px; font-size: 1.2rem;">
                ${this.getProductEmoji(product)}
              </div>
            </div>
            <div class="flex-fill">
              <div class="d-flex align-items-center">
                <h6 class="card-title mb-1 text-truncate me-2" style="font-size: 0.9rem;">
                  ${product.name || product.title || 'Unnamed Product'}
                </h6>
                ${hasVariants ? `
                  <span class="chevron-icon text-muted" style="font-size: 0.7rem; transition: transform 0.2s;">
                    ${chevronIcon}
                  </span>
                ` : ''}
              </div>
              <div class="d-flex justify-content-between align-items-center">
                <p class="card-text mb-0 text-muted" style="font-size: 0.8rem;">
                  SKU: ${product.sku || 'N/A'}
                </p>
                ${hasVariants ? `
                  <small class="text-muted">${variantsCount} variants</small>
                ` : ''}
              </div>
            </div>
          </div>
          ${variantsHtml}
              ${product.price ? `<p class="card-text mb-0 text-success fw-bold" style="font-size: 0.8rem;">$${product.price}</p>` : ''}
            </div>
            <div class="text-muted">
              <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"/>
              </svg>
            </div>
          </div>
        </div>
      </div>
    `
  }

  // Get emoji for product (simple logic, can be enhanced)
  getProductEmoji(product) {
    const name = (product.name || product.title || '').toLowerCase()
    if (name.includes('protein')) return '🥤'
    if (name.includes('vitamin')) return '💊'
    if (name.includes('energy')) return '⚡'
    if (name.includes('bottle') || name.includes('shaker')) return '🍼'
    if (name.includes('guide') || name.includes('book')) return '📖'
    return '📦'
  }

  // Handle drag start for products
  dragStart(event) {
    const productCard = event.currentTarget
    const productData = {
      id: productCard.dataset.productId,
      sku: productCard.dataset.productSku,
      name: productCard.dataset.productName,
      price: productCard.dataset.productPrice
    }

    event.dataTransfer.setData('application/json', JSON.stringify(productData))
    event.dataTransfer.effectAllowed = 'copy'
    
    // Add visual feedback
    productCard.classList.add('opacity-50')
    
    // Dispatch custom event
    this.dispatch('productDragStart', { detail: { product: productData } })
  }

  // Toggle variants visibility
  toggleVariants(event) {
    // Prevent drag when clicking on variants toggle
    event.stopPropagation()
    event.preventDefault()

    const productCard = event.currentTarget
    const productId = productCard.dataset.productId
    const variantsContainer = productCard.querySelector(`[data-product-variants="${productId}"]`)
    const chevronIcon = productCard.querySelector('.chevron-icon')

    if (variantsContainer && chevronIcon) {
      const isVisible = variantsContainer.style.display !== 'none'

      if (isVisible) {
        variantsContainer.style.display = 'none'
        chevronIcon.style.transform = 'rotate(0deg)'
        chevronIcon.textContent = '▶'
      } else {
        variantsContainer.style.display = 'block'
        chevronIcon.style.transform = 'rotate(90deg)'
        chevronIcon.textContent = '▼'
      }
    }
  }

  // UI Helper methods
  showLoading() {
    this.productsLoadingTarget.classList.remove('hidden')
  }

  hideLoading() {
    this.productsLoadingTarget.classList.add('hidden')
  }

  showEmpty() {
    this.productsEmptyTarget.classList.remove('hidden')
  }

  hideEmpty() {
    this.productsEmptyTarget.classList.add('hidden')
  }

  updateProductsCount(count) {
    if (this.hasProductsCountTarget && this.hasProductsCountNumberTarget) {
      this.productsCountNumberTarget.textContent = count
      if (count > 0) {
        this.productsCountTarget.classList.remove('hidden')
      } else {
        this.productsCountTarget.classList.add('hidden')
      }
    }
  }

  // Get emoji for product type
  getProductEmoji(product) {
    const name = (product.name || product.title || '').toLowerCase()

    if (name.includes('protein') || name.includes('powder')) return '🥤'
    if (name.includes('vitamin') || name.includes('supplement')) return '💊'
    if (name.includes('energy') || name.includes('drink')) return '⚡'
    if (name.includes('bar') || name.includes('snack')) return '🍫'
    if (name.includes('oil') || name.includes('omega')) return '🫒'
    if (name.includes('probiotic') || name.includes('digestive')) return '🦠'
    if (name.includes('weight') || name.includes('loss')) return '⚖️'
    if (name.includes('beauty') || name.includes('skin')) return '✨'

    return '📦' // Default product icon
  }
}
