<!-- Step 1: Bundle Information -->
<div class="step-content">

  <%= form_with model: @wizard, url: process_bundle_wizard_step_admin_bundles_path(step: 'info'),
                method: :post,
                data: {
                  turbo_frame: "_top",
                  turbo_action: "replace",
                  bundle_wizard_target: "form",
                  action: "turbo:submit-end->bundle-wizard#stepSubmitted turbo:submit-error->bundle-wizard#stepError"
                },
                class: "needs-validation",
                novalidate: true do |form| %>
    
    <div class="row justify-content-center">
      <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
          <div class="card-body p-3">

            <!-- Bundle Name -->
            <div class="mb-3">
              <%= form.label :name, "Bundle Name", class: "form-label fw-semibold" %>
              <%= form.text_field :name,
                    class: "form-control #{'is-invalid' if @bundle&.errors&.[](:name)&.any?}",
                    placeholder: "e.g., Yoli Transformation Bundle",
                    required: true %>
              <% if @bundle&.errors&.[](:name)&.any? %>
                <div class="invalid-feedback">
                  <%= @bundle.errors[:name].first %>
                </div>
              <% end %>
              <div class="form-text small">
                This will be the display name for your bundle that customers will see.
              </div>
            </div>

            <!-- Bundle SKU -->
            <div class="mb-3">
              <%= form.label :sku, "SKU (Stock Keeping Unit)", class: "form-label fw-semibold" %>
              <%= form.text_field :sku,
                    class: "form-control #{'is-invalid' if @bundle&.errors&.[](:sku)&.any?}",
                    placeholder: "Auto-generated from bundle name..." %>
              <% if @bundle&.errors&.[](:sku)&.any? %>
                <div class="invalid-feedback">
                  <%= @bundle.errors[:sku].first %>
                </div>
              <% end %>
              <div id="sku-suggestion" class="sku-suggestion" style="display: none;"></div>
              <div class="form-text small">
                Unique identifier for this bundle. Auto-filled from bundle name, but you can edit it.
              </div>
            </div>

            <!-- Bundle Description -->
            <div class="mb-3">
              <%= form.label :description, "Description", class: "form-label fw-semibold" %>
              <%= form.text_area :description,
                    class: "form-control #{'is-invalid' if @bundle&.errors&.[](:description)&.any?}",
                    rows: 3,
                    placeholder: "Describe what this bundle offers and its benefits..." %>
              <% if @bundle&.errors&.[](:description)&.any? %>
                <div class="invalid-feedback">
                  <%= @bundle.errors[:description].first %>
                </div>
              <% end %>
              <div class="form-text small">
                Optional description that will help customers understand this bundle (max 500 characters).
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>

    <!-- Navigation -->
    <div class="wizard-navigation mt-3 text-center">
      <%= link_to admin_bundles_path, class: "btn btn-outline-secondary me-3" do %>
        <span class="me-1">←</span>Cancel
      <% end %>

      <button type="submit"
              class="btn btn-primary"
              data-bundle-wizard-target="nextButton"
              data-action="click->bundle-wizard#nextStep">
        Continue to Builder <span class="ms-1">→</span>
      </button>
    </div>

  <% end %>
</div>

<style>
.step-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
}

.step-header h3 {
  color: #2c3e50;
  font-weight: 600;
}

.card {
  border-radius: 12px;
}

.form-control {
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
}

.form-label.fw-semibold {
  color: #495057;
  margin-bottom: 0.25rem;
  font-size: 0.95rem;
}

.form-text.small {
  font-size: 0.8rem;
  margin-top: 0.2rem;
}

.wizard-navigation {
  padding: 1.5rem 0 1rem 0;
}

.btn {
  padding: 0.5rem 1.5rem;
  font-size: 1rem;
}

.sku-suggestion {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  display: none;
}

.sku-suggestion button {
  background: none;
  border: none;
  color: #0066cc;
  text-decoration: underline;
  cursor: pointer;
  padding: 2px 4px;
  font-size: inherit;
  font-weight: 500;
}

.sku-suggestion button:hover {
  color: #004499;
  background-color: #e3f2fd;
  border-radius: 3px;
}

/* Reduce spacing between form groups */
.mb-3 {
  margin-bottom: 1rem !important;
}

/* Make textarea more compact */
textarea.form-control {
  resize: vertical;
  min-height: calc(1.5em + 1rem + 2px);
}
</style>

<script>
// Simple SKU generation - no Stimulus conflicts
(function() {
  'use strict';

  // Wait for DOM to be ready with a small delay
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(initSkuGeneration, 100);
    });
  } else {
    setTimeout(initSkuGeneration, 100);
  }

  function initSkuGeneration() {
    // Try multiple selectors to find the fields
    const nameField = document.getElementById('wizard_name') ||
                     document.querySelector('input[name="wizard[name]"]') ||
                     document.querySelector('input[placeholder*="Yoli"]');

    const skuField = document.getElementById('wizard_sku') ||
                    document.getElementById('bundle_wizard_sku') ||
                    document.querySelector('input[name="wizard[sku]"]') ||
                    document.querySelector('input[name="bundle_wizard[sku]"]') ||
                    document.querySelector('input[placeholder*="auto-generate"]') ||
                    document.querySelector('input[placeholder*="Auto-generated"]');

    const skuSuggestion = document.getElementById('sku-suggestion');

    // Debug: List all input fields
    const allInputs = document.querySelectorAll('input[type="text"]');
    console.log('🔍 All text inputs found:', Array.from(allInputs).map(input => ({
      id: input.id,
      name: input.name,
      placeholder: input.placeholder
    })));

    console.log('🔍 SKU Generation Init:', {
      nameField: !!nameField,
      skuField: !!skuField,
      skuSuggestion: !!skuSuggestion,
      nameFieldId: nameField?.id,
      skuFieldId: skuField?.id,
      nameFieldName: nameField?.name,
      skuFieldName: skuField?.name
    });

    if (!nameField || !skuField || !skuSuggestion) {
      console.warn('⚠️ SKU generation: Required elements not found');
      return;
    }

    let userEditedSku = false;

    // Track if user manually edited SKU
    skuField.addEventListener('input', function() {
      console.log('✏️ SKU manually edited:', this.value);
      userEditedSku = true;
      hideSuggestion();
    });

    // Auto-fill SKU when name changes
    nameField.addEventListener('input', function() {
      const nameValue = this.value.trim();
      console.log('📝 Name changed:', nameValue);

      if (!userEditedSku && nameValue) {
        const suggestedSku = generateSku(nameValue);
        console.log('💡 Auto-filling SKU:', suggestedSku);
        skuField.value = suggestedSku;
        showAutoFillMessage(suggestedSku);
      } else if (!nameValue) {
        skuField.value = '';
        hideAutoFillMessage();
      }
    });

    function generateSku(name) {
      return name
        .trim()
        .toUpperCase()
        .replace(/[^A-Z0-9\s]/g, '') // Remove special characters
        .replace(/\s+/g, '-')        // Replace spaces with hyphens
        .substring(0, 17)            // Limit length (leave room for -001)
        + '-001';                    // Add suffix
    }

    function showAutoFillMessage(sku) {
      console.log('✨ Auto-filled SKU:', sku);
      skuSuggestion.innerHTML =
        '<span class="text-success">✓ Auto-generated from bundle name</span>';
      skuSuggestion.style.display = 'block';
      skuSuggestion.style.backgroundColor = '#d4edda';
      skuSuggestion.style.borderColor = '#c3e6cb';
      skuSuggestion.style.color = '#155724';

      // Hide the message after 3 seconds
      setTimeout(hideAutoFillMessage, 3000);
    }

    function hideAutoFillMessage() {
      skuSuggestion.style.display = 'none';
    }
  }
})();
</script>
