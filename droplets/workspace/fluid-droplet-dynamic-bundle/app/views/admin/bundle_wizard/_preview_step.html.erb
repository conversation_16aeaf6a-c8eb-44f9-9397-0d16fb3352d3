<!-- Step 3: Preview & Save -->
<div class="space-y-6"
     data-controller="bundle-preview">

  <!-- Hidden data elements for Stimulus -->
  <script type="application/json" id="bundle-data">
    <%= raw({
      name: @wizard&.name || 'Untitled Bundle',
      sku: @wizard&.sku || 'No SKU',
      description: @wizard&.description || 'No description'
    }.to_json) %>
  </script>

  <script type="application/json" id="products-data">
    <%= raw((@wizard&.products || []).to_json) %>
  </script>

  <div class="text-center space-y-2">
    <h1 class="text-3xl font-bold text-gray-900">
      <span class="mr-2">👀</span>Review Your Bundle
    </h1>
    <p class="text-gray-600">Review your bundle configuration before saving it to your store.</p>
  </div>

  <div class="max-w-4xl mx-auto space-y-6">

    <!-- Bundle Overview Card -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="bg-blue-600 text-white px-6 py-4 rounded-t-lg flex justify-between items-center">
        <h2 class="text-lg font-semibold">
          <span class="mr-2">📦</span>Bundle Overview
        </h2>
        <button type="button"
                class="btn btn-outline-primary btn-sm"
                data-action="click->bundle-preview#editInfo">
          <i class="fas fa-edit me-1"></i>Edit Info
        </button>
      </div>
      <div class="p-6">
        <div class="grid md:grid-cols-3 gap-6">
          <div class="md:col-span-2 space-y-3">
            <h3 class="text-xl font-bold text-blue-600" data-bundle-preview-target="bundleName">Loading...</h3>
            <p class="text-gray-600">
              <span class="font-semibold">SKU:</span>
              <span class="font-mono text-sm" data-bundle-preview-target="bundleSku">Loading...</span>
            </p>
            <div class="text-gray-700" data-bundle-preview-target="bundleDescription">Loading...</div>
          </div>
          <div class="space-y-3">
            <div class="bg-green-50 border border-green-200 rounded-lg px-4 py-2 text-center">
              <div class="text-green-800 font-semibold">
                <span data-bundle-preview-target="productCount">0</span> Products
              </div>
            </div>
            <div class="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 text-center">
              <div class="text-blue-800 font-semibold">
                Total: <span data-bundle-preview-target="totalPrice">$0.00</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Products Preview -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-lg font-semibold text-gray-900">
          <span class="mr-2">📦</span>Selected Products
        </h2>
        <button type="button"
                class="btn btn-outline-primary btn-sm"
                data-action="click->bundle-preview#editProducts">
          <i class="fas fa-edit me-1"></i>Edit Products
        </button>
      </div>
      <div class="p-6">
        <div class="max-h-96 overflow-y-auto" data-bundle-preview-target="productsList">
          <!-- Products will be loaded here by Stimulus -->
          <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p class="mt-2 text-gray-600">Loading products...</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Summary -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">
          <span class="mr-2">⚙️</span>Configuration Summary
        </h2>
      </div>
      <div class="p-6">
        <div class="grid md:grid-cols-2 gap-6">
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="font-semibold text-gray-700">Bundle Type:</span>
              <span class="text-gray-600">Dynamic Bundle</span>
            </div>
            <div class="flex justify-between">
              <span class="font-semibold text-gray-700">Created Via:</span>
              <span class="text-gray-600">Wizard</span>
            </div>
            <div class="flex justify-between">
              <span class="font-semibold text-gray-700">Status:</span>
              <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm font-medium">Draft</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="font-semibold text-gray-700">Products:</span>
              <span class="text-gray-600" data-bundle-preview-target="productCount">0</span>
            </div>
            <div class="flex justify-between">
              <span class="font-semibold text-gray-700">Total Value:</span>
              <span class="text-gray-600" data-bundle-preview-target="totalPrice">$0.00</span>
            </div>
            <div class="flex justify-between">
              <span class="font-semibold text-gray-700">Created:</span>
              <span class="text-gray-600"><%= Time.current.strftime("%B %d, %Y") %></span>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>

  <!-- Navigation -->
  <div class="wizard-actions">
    <div>
      <button type="button"
              class="btn btn-outline-secondary"
              data-wizard-target="prevButton"
              data-action="click->wizard#prevStep">
        <span class="me-1">←</span>Back to Builder
      </button>
    </div>
    <div class="d-flex gap-3">
      <%= link_to "Cancel", admin_bundles_path,
          class: "btn btn-outline-danger",
          onclick: "return confirm('Are you sure you want to cancel? All progress will be lost.')" %>

      <button type="button"
              class="btn btn-success btn-lg"
              data-bundle-preview-target="completeButton"
              data-action="click->bundle-preview#completeBundle">
        <span class="me-2">🚀</span>Complete Bundle
      </button>
    </div>
  </div>

  <!-- Hidden form for completion -->
  <%= form_with url: complete_bundle_wizard_admin_bundles_path,
                method: :post,
                id: 'bundle-complete-form',
                class: 'hidden' do |form| %>
    <!-- Form will be submitted by Stimulus controller -->
  <% end %>
</div>
